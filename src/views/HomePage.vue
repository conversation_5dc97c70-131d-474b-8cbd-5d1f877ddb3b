<template>
    <ion-page>
        <!-- Spinner -->
        <div v-if="isLoading" class="spinner-overlay">
            <ion-spinner name="bubbles"></ion-spinner>
            <div class="loading-text">Please wait...</div>
        </div>
        <Toolbar />
        <ActionButtons />
        <component :is="activeDashboard" v-if="activeDashboard" />
        <ion-content v-else>
            <div class="container">
                <h4 style="width: 100%; text-align: center; font-weight: 700">Welcome to MAHIS</h4>
                <p style="width: 100%; text-align: center">Please select a module to get started</p>
            </div>
        </ion-content>
    </ion-page>
</template>

<script setup lang="ts">
import { IonPage } from "@ionic/vue";
import { ref, onMounted, watch, computed } from "vue";
import { useRoute } from "vue-router";
import Toolbar from "@/components/Toolbar.vue";
import NCDDashboard from "@/apps/NCD/components/NCDDashboard.vue";
import ARTDashboard from "@/apps/ART/components/ARTDashboard.vue";
import ImmunizationDashboard from "@/apps/Immunization/components/ImmunizationDashboard.vue";
import OPDDashboard from "@/apps/OPD/components/OPDDashboard.vue";
import ANCDashboard from "@/apps/ANC/components/ANCDashboard.vue";
import LabourDashboard from "@/apps/LABOUR/components/LabourDashboard.vue";
import PNCDashboard from "@/apps/PNC/components/PNCDashboard.vue";
import { resetDemographics } from "@/services/reset_data";
import { useGlobalPropertyStore } from "@/stores/GlobalPropertyStore";
import { useUserActivities } from "@/composables/useUserActivities";
import { useUserRole } from "@/composables/useUserRole";
import { useProgramStore } from "@/stores/ProgramStore";
import { storeToRefs } from "pinia";
import { useWorkerStore } from "@/stores/workerStore";
import { ProgramId } from "@/services/program_service";
import { useStatusStore } from "@/stores/StatusStore";
import { Service } from "@/services/service";
import { UserService } from "@/services/user_service";
import { LocationService } from "@/services/location_service";
import ActionButtons from "@/components/ActionButtons.vue";
import { isEmpty } from "lodash";
import { modal } from "@/utils/modal";
import ModulePicker from "@/components/DashboardModal/ModulePicker.vue";

const isLoading = ref(true);
const route = useRoute();
const workerStore = useWorkerStore();

useUserRole();
const programStore = useProgramStore();
const { apiStatus } = storeToRefs(useStatusStore());
const { activeProgram } = storeToRefs(programStore);

const activeDashboard = computed(() => {
    switch (activeProgram.value.program_id) {
        case ProgramId.IMMUNIZATION_PROGRAM:
            return ImmunizationDashboard;
        case ProgramId.OPD_PROGRAM:
            return OPDDashboard;
        case ProgramId.NCD_PROGRAM:
            return NCDDashboard;
        case ProgramId.ANC_PROGRAM:
            return ANCDashboard;
        case ProgramId.PNC_PROGRAM:
            return PNCDashboard;
        case ProgramId.LABOUR_AND_DELIVERY_PROGRAM:
            return LabourDashboard;
        case ProgramId.HIV_PROGRAM:
            return ARTDashboard;
        default:
            return null;
    }
});

watch(
    () => route.name,
    async (newRoute) => {
        if (newRoute === "Home") {
            resetDemographics();
            await useGlobalPropertyStore().loadGlobalProperty();
            workerStore.initWorker();
            workerStore.postData("SYNC_ALL_DATA");
        }
    },
    { immediate: true, deep: true }
);
const getUserLocation = async () => {
    try {
        const userId: any = Service.getUserID();
        const user_data = await UserService.getUserByID(userId);
        if (user_data.location_id != null) {
            const response = await LocationService.getLocation(user_data.location_id);
            localStorage.setItem("locationData", JSON.stringify(response));
        }
    } catch (error) {
        console.error("Failed to retrieve location data:", error);
    }
};
if (apiStatus.value) {
    useUserActivities();
    getUserLocation();
}
onMounted(async () => {
    try {
        isLoading.value = true;
        workerStore.terminate();
        resetDemographics();
        await useGlobalPropertyStore().loadGlobalProperty();
        if(isEmpty(activeProgram.value)){
            await modal.show(ModulePicker, {}, "module-picker-modal");
        }
    } catch (error) {
        console.error("Error initializing component:", error);
    } finally {
        isLoading.value = false;
    }
});
</script>