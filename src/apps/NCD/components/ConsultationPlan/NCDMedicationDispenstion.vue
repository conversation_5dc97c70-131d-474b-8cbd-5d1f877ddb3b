<template>
    <ion-page>
        <NavigationMenu ref="navMenuRef" />
        <ViewToggleComponent :initial-view="NCDMedicatioTogglePreference.toggle_view" @view-changed="handleViewChange" />
        <ion-content>
            <!-- No Medications Message -->
            <div style="margin: 20px;" v-if="Object.keys(medicationsByProgram).length === 0" class="flex flex-col items-center justify-center p-8">
                <ion-icon :icon="alertCircleOutline" size="large" class="mb-4 text-gray-500"></ion-icon>
                <h2 class="text-xl font-semibold text-gray-700 mb-2">No Medications to Dispense</h2>
                <p class="text-gray-600">There are currently no medications waiting to be dispensed for current Client.</p>
            </div>

            <!-- List View -->
            <div class="p-4" v-else-if="NCDMedicatioTogglePreference.toggle_view === 'list'">
                <template v-for="(medications, programName) in medicationsByProgram" :key="programName">
                    <h2 class="text-xl font-bold mb-4 program-name">{{ programName }}</h2>
                    <ion-card class="mb-6">
                        <ion-card-content>
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="bg-gray-100">
                                            <th class="p-3 text-left">Date</th>
                                            <th class="p-3 text-left">Medication</th>
                                            <th class="p-3 text-left">Dose</th>
                                            <th class="p-3 text-left">Frequency</th>
                                            <th class="p-3 text-left">Amount to Dispense</th>
                                            <th class="p-3 text-left">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="medication in medications" :key="medication.order_id" class="border-b hover:bg-gray-50">
                                            <td class="p-3">
                                                {{ formatHeaderDate(medication.order.date_created.split("T")[0]) }}
                                            </td>
                                            <td class="p-3">
                                                <div class="flex items-center">
                                                    <ion-icon :icon="medkit" class="mr-2"></ion-icon>
                                                    {{ medication.drug.name }}
                                                </div>
                                            </td>
                                            <td class="p-3">{{ medication.dose }} {{ medication.units }}</td>
                                            <td class="p-3">
                                                {{ getFrequencyLabel(medication.frequency) }}
                                            </td>
                                            <td class="p-3">
                                                <div class="flex items-center space-x-2">
                                                    <ion-input
                                                        type="number"
                                                        placeholder="Amount"
                                                        v-model="medication.amountToDispense"
                                                        @input="validateAmount(medication)"
                                                        :class="['w-24 dose-input bordered-input', medication.error ? 'input-error' : '']"
                                                    />
                                                    <ion-button
                                                        v-if="false"
                                                        size="small"
                                                        color="light"
                                                        @click="setAmountAsPrescribed(medication)"
                                                        style="margin-top: 10px"
                                                    >
                                                        <ion-icon :icon="clipboardOutline" size="small" class="mr-1"></ion-icon>
                                                        <span class="text-green-700">As Prescribed</span>
                                                    </ion-button>
                                                </div>
                                                <div v-if="medication.error" class="error-text text-sm">
                                                    {{ medication.error }}
                                                </div>
                                            </td>
                                            <td class="p-3">
                                                <div class="flex space-x-2">
                                                    <ion-button
                                                        color="primary"
                                                        size="small"
                                                        @click="dispenseMedication(medication)"
                                                        :disabled="!medication.amountToDispense || medication.dispensed"
                                                    >
                                                        <ion-icon :icon="checkmarkDoneCircleOutline" class="mr-1" style="margin-right: 5px"></ion-icon>
                                                        {{ medication.dispensed ? "Dispensed" : "Dispense" }}
                                                    </ion-button>
                                                    <ion-button color="secondary" size="small" @click="viewDetails(medication)" style="margin-left: 10px">
                                                        <ion-icon :icon="eye" class="mr-1" style="margin-right: 5px"></ion-icon>
                                                        Details
                                                    </ion-button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div v-if="medications.length === 0" class="text-center py-8">
                                    <ion-icon :icon="alertCircleOutline" size="large" class="mb-2"></ion-icon>
                                    <p>No medications prescribed for {{ programName }}</p>
                                </div>
                            </div>
                        </ion-card-content>
                    </ion-card>
                </template>
            </div>

            <!-- Card View -->
            <div class="p-4" v-else>
                <template v-for="(medications, programName) in medicationsByProgram" :key="programName">
                    <h2 class="text-xl font-bold mb-4 program-name">{{ programName }}</h2>
                    <template v-for="(medicationGroup, date) in groupMedicationsByDate(medications)" :key="date">
                        <h3 class="text-lg font-bold mb-2">{{ formatHeaderDate(date) }}</h3>
                        <ion-row>
                            <!-- Responsive Column for Date Header -->
                            <ion-col size="12" size-md="6" size-lg="4">
                                <h3 class="text-md font-medium">{{ date }}</h3>
                            </ion-col>
                        </ion-row>
                        <ion-row>
                            <!-- Each Medication Card in its Column -->
                            <ion-col v-for="medication in medicationGroup" :key="medication.order_id" size="12" size-md="6" size-lg="4">
                                <ion-card class="mb-4">
                                    <ion-card-header>
                                        <ion-card-title class="medication-details-header">
                                            <ion-icon :icon="medkit" class="ion-margin-end"></ion-icon>
                                            <span>{{ medication.drug.name }}</span>
                                        </ion-card-title>
                                    </ion-card-header>
                                    <ion-card-content>
                                        <div class="medication-details">
                                            <div class="flex justify-between mb-3">
                                                <!-- <ion-icon :icon="timeOutline" class="mr-2"></ion-icon> -->
                                                <!-- <span class="flex items-center">
                                                    <ion-icon :icon="medication" class="mr-2"></ion-icon>
                                                     {{ medication.dose }} {{ medication.units }}
                                                </span> -->
                                                <span class="flex items-center">
                                                    {{ getFrequencyLabel(medication.frequency) }}
                                                    ({{ medication.frequency }})
                                                </span>
                                            </div>
                                            <div class="flex items-center mb-3">
                                                <ion-input
                                                        type="number"
                                                        placeholder="Amount"
                                                        v-model="medication.amountToDispense"
                                                        @input="validateAmount(medication)"
                                                        :class="['w-24 dose-input bordered-input', medication.error ? 'input-error' : '']"
                                                    />
                                                <ion-button
                                                    v-if="false"
                                                    size="small"
                                                    color="light"
                                                    style="margin-top: 10px"
                                                    @click="setAmountAsPrescribed(medication)"
                                                >
                                                    <ion-icon :icon="clipboardOutline" size="small" style="margin-right: 5px" class="mr-1"></ion-icon>
                                                    <span style="color: darkseagreen"> As Prescribed</span>
                                                </ion-button>
                                            </div>
                                            <div v-if="medication.error" class="error-text">
                                                {{ medication.error }}
                                            </div>
                                            <div class="mt-3 flex space-x-2">
                                                <ion-button
                                                    color="primary"
                                                    @click="dispenseMedication(medication)"
                                                    :disabled="!medication.amountToDispense || medication.dispensed"
                                                >
                                                    <ion-icon :icon="checkmarkDoneCircleOutline" class="mr-2" style="margin-right: 5px"></ion-icon>
                                                    {{ medication.dispensed ? "Dispensed" : "Dispense" }}
                                                </ion-button>
                                                <ion-button color="secondary" style="margin-left: 20px" @click="viewDetails(medication)">
                                                    <ion-icon :icon="eye" class="mr-2" style="margin-right: 5px"></ion-icon>
                                                    Details
                                                </ion-button>
                                            </div>
                                        </div>
                                    </ion-card-content>
                                </ion-card>
                            </ion-col>
                        </ion-row>
                    </template>
                    <div v-if="medications.length === 0" class="text-center mb-8">
                        <ion-icon :icon="alertCircleOutline" size="large" class="mb-2"></ion-icon>
                        <p>No medications prescribed for {{ programName }}</p>
                    </div>
                </template>
            </div>
        </ion-content>
        <ion-footer collapse="fade" class="ion-no-border">
            <ion-row>
                <ion-col>
                    <ion-button id="cbtn" class="" color="primary" fill="solid" style="float: right; margin-left: 50px" @click="printVisitSummary()">
                        Print Visit Summary
                    </ion-button>
                    <ion-button id="cbtn" class="" color="danger" fill="solid" style="float: right; margin-left: 50px" @click="closeVisit()">
                        Close visit
                    </ion-button>
                </ion-col>
            </ion-row>
        </ion-footer>
        <ion-footer></ion-footer>
    </ion-page>
</template>

<script lang="ts">
import {
    IonContent,
    IonPage,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonButton,
    IonModal,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonInput,
    IonRow,
    IonCol,
    IonIcon,
    IonFooter,
} from "@ionic/vue";
import { defineComponent, ref } from "vue";
import { mapState } from "pinia";
import { EIRreportsStore } from "@/apps/Immunization/stores/EIRreportsStore";
import NavigationMenu from "@/apps/Immunization/components/Reports/NavigationMenu.vue";
import ViewToggleComponent from "@/apps/NCD/components/ViewToggleComponent.vue";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { DrugOrderService } from "@/services/drug_order_service";
import { DRUG_FREQUENCIES } from "@/services/drug_prescription_service";
import MedicationDetailsModal from "./MedicationDetailsModal.vue";
import { createModal, toastDanger, toastSuccess } from "@/utils/Alerts";
import { DispensationService } from "@/apps/NCD/services/dispensation_service";
import { medkit, repeat, eye, alertCircleOutline, checkmarkDoneCircleOutline, clipboardOutline, timeOutline } from "ionicons/icons";
import { PrintoutService } from "@/services/printout_service";
import { getUserLocation } from "@/services/userService";
import { PatientOpdList } from "@/services/patient_opd_list";
import dates from "@/utils/Date";
import { usePatientList } from "@/apps/OPD/stores/patientListStore";
import { checkProgramMatch } from "@/stores/GlobalPropertyStore";
import { useGeneralStore } from "@/stores/GeneralStore";
import { getOfflineRecords } from "@/services/offline_service";

export default defineComponent({
    name: "MedicationDispensation",
    components: {
        IonContent,
        IonPage,
        NavigationMenu,
        IonCard,
        IonCardHeader,
        IonCardTitle,
        IonCardContent,
        IonButton,
        IonModal,
        IonHeader,
        IonToolbar,
        IonTitle,
        IonButtons,
        IonInput,
        MedicationDetailsModal,
        IonRow,
        IonCol,
        IonIcon,
        ViewToggleComponent,
        IonFooter,
    },
    setup() {
        const navMenuRef = ref<InstanceType<typeof NavigationMenu> | null>(null);
        
        const navigateBack = () => {
        if (navMenuRef.value) {
            navMenuRef.value.goBackwards();
        }
        };
        
        return {
            navMenuRef,
            navigateBack
        };
    },
    data() {
        return {
            medicationsByProgram: {} as any,
            medications: [] as any,
            selectedMedication: null as any,
            medkit,
            repeat,
            eye,
            alertCircleOutline,
            checkmarkDoneCircleOutline,
            clipboardOutline,
            timeOutline,
            currentView: "list",
            previousClinicalNotes: "" as any,
            checkedIn: false as Boolean,
            showAsPrescribedBtn: false as Boolean,
            isOnline: false
        };
    },
    computed: {
        ...mapState(EIRreportsStore, ["navigationPayload"]),
        ...mapState(useDemographicsStore, ["patient"]),
        ...mapState(useGeneralStore, ["NCDMedicatioTogglePreference"]),
        medicationsByProgram(): Record<string, any[]> {
            return this.medications.reduce((groups: Record<string, any[]>, medication: any) => {
                const programName = medication.program_id; // Assuming medication has a program_id field
                if (!groups[programName]) {
                    groups[programName] = [];
                }
                groups[programName].push(medication);
                return groups;
            }, {});
        },
        groupedMedications() {
            return this.medications.reduce((groups: any, medication: any) => {
                const date = medication.order.date_created.split("T")[0];
                if (!groups[date]) {
                    groups[date] = [];
                }
                groups[date].push(medication);
                return groups;
            }, {}) as any;
        },
    },
    async mounted() {
        this.initComp();
    },
    methods: {
        async initComp() {
            this.initTogglePreference();
            this.medications = [];
            this.initOwnNavData();
            await this.prescribedMedications();
            this.enableShowAsPrescribedBtn();
        },
        async getPrograms() {
            // if (this.isOnline) {
            //     // Fetch programs via API when online
            //     // Uncomment the following line when the API is available
            //     return await this.getOnlinePrograms();
            // } else {
            //     // Fall back to offline programs when offline
            //     return await this.getOfflinePrograms();
            // }
            return await this.getOfflinePrograms();
        },
        async printVisitSummary() {
            await new PrintoutService().printData("visit");
        },
        async closeVisit() {
            try {
                const visit = await PatientOpdList.getCheckInStatus(this.patient.patientID);
                await PatientOpdList.checkOutPatient(visit[0].id, dates.todayDateFormatted());
                const location: any = await getUserLocation();
                const locationId = location ? location.code : null;
                await usePatientList().refresh(locationId);
                this.checkedIn = false;
                toastSuccess("The patient's visit is now closed", 50000);
                this.navigateBack();
            } catch (e) {
                console.error(e);
                toastDanger("An error occurred while closing the visit.", 50000);
                this.navigateBack();
            }
        },
        initOwnNavData() {
            const store = EIRreportsStore();
            store.setNavigationPayload("Dispense Medication", true, false, "/", "home");
        },
        async prescribedMedications() {
            if (this.isOnline) {
                // Prioritize API call when online
                this.medications = await DrugOrderService.findProgramDrugOrdersAwaitingDispensation(this.patient.patientID);
                console.log(this.medications);
                
                // Process medications to match the same format as offline version
                this.medications = this.medications.map((med: any) => ({
                    ...med,
                    amountToDispense: med.amountToDispense || null,
                    dispensed: med.dispensed || false,
                })) as any;
                
                await this.groupMedicationsByProgram(this.medications);
            } else {
                // Fall back to offline data when offline
                await this.getPatientPrescribedMedications();
            }
        },
        async getPatientPrescribedMedications() {
            try {
                const medicationsPendingDispension = this.patient.dispensations.saved;
                const unsavedMedications = this.patient.dispensations.unsaved;

                const unsavedOrderIds = new Set();
                unsavedMedications.forEach((medication: any) => {
                    if (medication.dispensations && medication.dispensations[0] && medication.dispensations[0].drug_order_id) {
                        unsavedOrderIds.add(medication.dispensations[0].drug_order_id);
                    }
                });
                
                const filteredMedications = medicationsPendingDispension.filter(
                    (medication: any) => !unsavedOrderIds.has(medication.order_id)
                );
                
                const processedMedications = filteredMedications.map((medication: any) => ({
                    ...medication,
                    amountToDispense: medication.amountToDispense || null,
                    dispensed: medication.dispensed || false,
                }));
                
                await this.groupMedicationsByProgram(processedMedications); 
            } catch (error) {
                console.error("Error fetching offline medications:", error);
            }
        },
        async groupMedicationsByProgram(medications: any[]) {
            const programs = await this.getPrograms();
            
            const programMap = {} as any;
            programs.forEach((program: any) => {
                programMap[program.program_id] = program.name;
            });
            
            this.medicationsByProgram = {};
            
            medications.forEach((medication: any) => {
                const programName = programMap[medication.program_id];
                
                if (programName) {
                    if (!this.medicationsByProgram[programName]) {
                        this.medicationsByProgram[programName] = [];
                    }
                    
                    this.medicationsByProgram[programName].push(medication);
                }
            });
        },
        async getOfflinePrograms() {
            try {
                const name = "",
                    page = 1,
                    page_size = 50000;
    
                const likeClause = name ? { name: `%${name}%` } : {};
    
                const programs = await getOfflineRecords("programs", {
                    likeClause,
                    currentPage: page,
                    itemsPerPage: page_size,
                }).then((data: any) => data.records);
    
                return programs;
            } catch (error: any) {
                console.error("Error fetching offline districts:", error.message);
                throw error;
            }
        },
        getFrequencyLabel(code: any) {
            const frequency = DRUG_FREQUENCIES.find((f) => f.code === code);
            return frequency ? frequency.label : "Unknown";
        },
        validateAmount(medication: any) {
            const value = parseFloat(medication.amountToDispense);
            if (isNaN(value) || value <= 0) {
                medication.error = "Please enter a valid amount greater than 0.";
            } else {
                medication.error = null;
            }
        },
        setAmountAsPrescribed(medication: any) {
            medication.amountToDispense = medication.dose;
            this.validateAmount(medication);
        },
        async dispenseMedication(medication: any) {
            if (medication.error) {
                toastDanger("Fix errors before dispensing.");
                return;
            }
            try {
                const dispensationService = new DispensationService();
                const dispensationArryObj = dispensationService.getDispensationArryObj(medication.order_id, medication.amountToDispense);

                if (this.isOnline) {
                    // Save dispensation via API when online
                    await dispensationService.saveDispensations(dispensationArryObj, medication.program_id);
                    medication.dispensed = true;
                    toastSuccess("Medication dispensed successfully.");
                } else {
                    // Save dispensation offline when offline
                    const offlineDispensationService = new DispensationService();
                    const offlineDispensationArryObj = offlineDispensationService.generateOfflineDispensatiobObject(dispensationArryObj, medication.program_id) as any;
                    await offlineDispensationService.saveDispensationsOffline([offlineDispensationArryObj]);
                    medication.dispensed = true;
                }
            } catch (error) {
                toastDanger("Dispensing failed. Please try again.");
                toastDanger("Error: " + error);
                console.error(error);
            }
        },
        async viewDetails(medication: any) {
            this.selectedMedication = medication;
            await createModal(MedicationDetailsModal, { class: "large-modal" }, true, { selectedMedication: this.selectedMedication });
        },
        formatHeaderDate(dateString: any) {
            return new Date(dateString).toLocaleDateString("en-US", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
            });
        },
        handleViewChange(view: string) {
            // this.currentView = view;
            this.initTogglePreference(view, true);
        },
        async enableShowAsPrescribedBtn() {
            const isMatched = await checkProgramMatch([14]);
            this.showAsPrescribedBtn = !isMatched;
        },
        initTogglePreference(toggle_view = "list", updateStore = false) {
            const preference = { toggle_view: toggle_view };
            const generalStore = useGeneralStore();
            if (Object.keys(this.NCDMedicatioTogglePreference).length === 0) {
                preference.toggle_view = "list";
                generalStore.setNCDMedicatioTogglePreference(preference);
            }

            if (Object.keys(this.NCDMedicatioTogglePreference).length > 0 && updateStore == true) {
                generalStore.setNCDMedicatioTogglePreference(preference);
            }
        },
        groupMedicationsByDate(medications: any) {
            return medications.reduce((groups: any, medication: any) => {
                const date = medication.order.date_created.split("T")[0];
                if (!groups[date]) {
                    groups[date] = [];
                }
                groups[date].push(medication);
                return groups;
            }, {});
        },
    },
    watch: {
        patient: {
            async handler() {
                this.initComp();
            },
            deep: true,
        },
        $route: {
            handler(to) {
                if (to.name === "NCDDispensations") {
                    this.initComp();
                }
            },
            immediate: true,
        },
    },
});
</script>
<style scoped>
.dose-input {
    --padding-start: 8px;
    --padding-end: 8px;
    margin-left: 10px;
    width: 200px;
}

.bordered-input {
    border: 2px solid #ccc;
    border-radius: 4px;
    padding: 4px;
}

.medication-details-header {
    display: flex;
    align-items: center;
    gap: 8px;
}

.input-error {
    border-color: red;
}

.error-text {
    color: red;
    font-size: 0.875rem;
    margin-top: 4px;
}

.dose-input {
    --padding-start: 8px;
    --padding-end: 8px;
    border: 2px solid #ccc;
    border-radius: 4px;
    padding: 4px;
}

.input-error {
    border-color: red;
}

.error-text {
    color: red;
}

table {
    border-collapse: collapse;
    width: 100%;
}

th {
    font-weight: 600;
    text-align: left;
}

td,
th {
    padding: 12px;
    border-bottom: 1px solid #e2e8f0;
}

tr:hover {
    background-color: #f8fafc;
}

.program-name {
    color: #0e7e20;
    font-weight: 600;
    font-size: 18px;
    margin-left: 10px;
}
</style>
