<template>
    <ion-grid>
        <!-- Error Messages -->
        <ion-row v-if="Object.keys(validationMessages).some(key => validationMessages[key])">
            <ion-col size="12">
                <ion-note color="danger">
                    <div v-for="(message, field) in validationMessages" :key="field">
                        <div v-if="message">{{ message }}</div>
                    </div>
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Phone and Home Visit Followup -->
        <ion-row v-if="formModel.followup.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">Phone Followup <span style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.phone_followup"
                    @ionChange="(e) => { formData.phone_followup = e.detail.value; dataHandler('phone_followup'); }">
                    <ion-item lines="none">
                        <ion-radio slot="start" value="Yes"></ion-radio>
                        <ion-label>Yes</ion-label>
                    </ion-item>
                    <ion-item lines="none">
                        <ion-radio slot="start" value="No"></ion-radio>
                        <ion-label>No</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.phone_followup" color="danger">
                    {{ validationMessages.phone_followup }}
                </ion-note>
            </ion-col>
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">Home Visit Followup <span style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.home_visit_followup"
                    @ionChange="(e) => { formData.home_visit_followup = e.detail.value; dataHandler('home_visit_followup'); }">
                    <ion-item lines="none">
                        <ion-radio slot="start" value="Yes"></ion-radio>
                        <ion-label>Yes</ion-label>
                    </ion-item>
                    <ion-item lines="none">
                        <ion-radio slot="start" value="No"></ion-radio>
                        <ion-label>No</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.home_visit_followup" color="danger">
                    {{ validationMessages.home_visit_followup }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Has Linkage -->
        <ion-row v-if="formModel.has_linkage.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.has_linkage.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.has_linkage"
                    @ionChange="(e) => { formData.has_linkage = e.detail.value; dataHandler('has_linkage'); }">
                    <ion-item lines="none" v-for="option in formModel.has_linkage.options()" :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.has_linkage" color="danger">
                    {{ validationMessages.has_linkage }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Linkage Number -->
        <ion-row v-if="formModel.has_linkage.linkage_number.required()">
            <ion-col size="12" size-md="6">
                <BasicInputField
                    :inputHeader="formModel.has_linkage.linkage_number.label + (formModel.has_linkage.linkage_number.required() ? '*' : '')"
                    placeholder="e.g 192" :inputValue="formData.linkage_number"
                    @update:inputValue="(value) => { formData.linkage_number = value; dataHandler('linkage_number'); }"
                    :error="!!validationMessages.linkage_number">
                    <template #end>
                        <ion-label>set_site_prefix</ion-label>
                    </template>
                </BasicInputField>
                <ion-note v-if="validationMessages.linkage_number" color="danger">
                    {{ validationMessages.linkage_number }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Received ARVs -->
        <ion-row v-if="formModel.has_linkage.received_arvs.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.has_linkage.received_arvs.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.received_arvs"
                    @ionChange="(e) => { formData.received_arvs = e.detail.value; dataHandler('received_arvs'); }">
                    <ion-item lines="none" v-for="option in formModel.has_linkage.received_arvs.options()"
                        :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.received_arvs" color="danger">
                    {{ validationMessages.received_arvs }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Date last taken ARVs -->
        <ion-row v-if="formModel.date_last_taken_arvs.required()">
            <ion-col size="12" size-md="6">
                <DatePicker
                    :place_holder="formModel.date_last_taken_arvs.label + (formModel.date_last_taken_arvs.required() ? '*' : '')"
                    :date_prop="formData.date_last_taken_arvs"
                    @update:date="(value) => { formData.date_last_taken_arvs = value; dataHandler('date_last_taken_arvs'); }"
                    :error="!!validationMessages.date_last_taken_arvs" />
                <ion-note v-if="validationMessages.date_last_taken_arvs" color="danger">
                    {{ validationMessages.date_last_taken_arvs }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Taken last two months -->
        <ion-row v-if="formModel.taken_last_two_months.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.taken_last_two_months.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.taken_last_two_months"
                    @ionChange="(e) => { formData.taken_last_two_months = e.detail.value; dataHandler('taken_last_two_months'); }">
                    <ion-item lines="none" v-for="option in formModel.taken_last_two_months.options()" :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.taken_last_two_months" color="danger">
                    {{ validationMessages.taken_last_two_months }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Taken last two weeks -->
        <ion-row v-if="formModel.taken_last_two_weeks.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.taken_last_two_weeks.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.taken_last_two_weeks"
                    @ionChange="(e) => { formData.taken_last_two_weeks = e.detail.value; dataHandler('taken_last_two_weeks'); }">
                    <ion-item lines="none" v-for="option in formModel.taken_last_two_weeks.options()" :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.taken_last_two_weeks" color="danger">
                    {{ validationMessages.taken_last_two_weeks }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Ever registered at ART clinic -->
        <ion-row v-if="formModel.ever_registered_at_art_clinic.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.ever_registered_at_art_clinic.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.ever_registered_at_art_clinic"
                    @ionChange="(e) => { formData.ever_registered_at_art_clinic = e.detail.value; dataHandler('ever_registered_at_art_clinic'); }">
                    <ion-item lines="none" v-for="option in formModel.ever_registered_at_art_clinic.options()"
                        :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.ever_registered_at_art_clinic" color="danger">
                    {{ validationMessages.ever_registered_at_art_clinic }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Location of ART Initiation -->
        <ion-row v-if="formModel.location_of_art_initialization.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.location_of_art_initialization.label }} <span
                        style="color: red">*</span></ion-label>
                <SelectFacility :show_error="!!validationMessages.location_of_art_initialization"
                    :selected_district_ids="[]" :selected_location="formData.location_of_art_initialization"
                    @facilitySelected="(value: any) => { formData.location_of_art_initialization = value; dataHandler('location_of_art_initialization'); }" />
                <ion-note v-if="validationMessages.location_of_art_initialization" color="danger">
                    {{ validationMessages.location_of_art_initialization }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- ART Number at Previous Location -->
        <ion-row v-if="formModel.art_number_at_previous_location.required()">
            <ion-col size="12" size-md="6">
                <BasicInputField
                    :inputHeader="formModel.art_number_at_previous_location.label + (formModel.art_number_at_previous_location.required() ? '*' : '')"
                    placeholder="Enter previous ART number" :inputValue="formData.art_number_at_previous_location"
                    @update:inputValue="(value) => { formData.art_number_at_previous_location = value; dataHandler('art_number_at_previous_location'); }"
                    :error="!!validationMessages.art_number_at_previous_location" />
                <ion-note v-if="validationMessages.art_number_at_previous_location" color="danger">
                    {{ validationMessages.art_number_at_previous_location }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- ART Start Date -->
        <ion-row v-if="formModel.art_start_date.required()">
            <ion-col size="12" size-md="6">
                <DatePicker
                    :place_holder="formModel.art_start_date.label + (formModel.art_start_date.required() ? '*' : '')"
                    :date_prop="formData.art_start_date"
                    @update:date="(value) => { formData.art_start_date = value; dataHandler('art_start_date'); }"
                    :error="!!validationMessages.art_start_date" />
                <ion-note v-if="validationMessages.art_start_date" color="danger">
                    {{ validationMessages.art_start_date }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Has Transfer Letter -->
        <ion-row v-if="formModel.has_transfer_letter.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.has_transfer_letter.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.has_transfer_letter"
                    @ionChange="(e) => { formData.has_transfer_letter = e.detail.value; dataHandler('has_transfer_letter'); }">
                    <ion-item lines="none" v-for="option in formModel.has_transfer_letter.options()" :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.has_transfer_letter" color="danger">
                    {{ validationMessages.has_transfer_letter }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- ART Registration Date -->
        <ion-row v-if="formModel.art_registration_date.required()">
            <ion-col size="12" size-md="6">
                <DatePicker
                    :place_holder="formModel.art_registration_date.label + (formModel.art_registration_date.required() ? '*' : '')"
                    :date_prop="formData.art_registration_date"
                    @update:date="(value) => { formData.art_registration_date = value; dataHandler('art_registration_date'); }"
                    :error="!!validationMessages.art_registration_date" />
                <ion-note v-if="validationMessages.art_registration_date" color="danger">
                    {{ validationMessages.art_registration_date }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Initial Weight and Height -->
        <ion-row>
            <ion-col size="12" size-md="6" v-if="formModel.initial_weight.required()">
                <BasicInputField
                    :inputHeader="formModel.initial_weight.label + (formModel.initial_weight.required() ? '*' : '')"
                    placeholder="1" inputType="number" :inputValue="formData.initial_weight"
                    @update:inputValue="(value) => { formData.initial_weight = value; dataHandler('initial_weight'); }"
                    :error="!!validationMessages.initial_weight" unit="KG" />
                <ion-note v-if="validationMessages.initial_weight" color="danger">
                    {{ validationMessages.initial_weight }}
                </ion-note>
            </ion-col>
            <ion-col size="12" size-md="6" v-if="formModel.initial_height.required()">
                <BasicInputField
                    :inputHeader="formModel.initial_height.label + (formModel.initial_height.required() ? '*' : '')"
                    placeholder="1" inputType="number" :inputValue="formData.initial_height"
                    @update:inputValue="(value) => { formData.initial_height = value; dataHandler('initial_height'); }"
                    :error="!!validationMessages.initial_height" unit="CM" />
                <ion-note v-if="validationMessages.initial_height" color="danger">
                    {{ validationMessages.initial_height }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- CD4 Available -->
        <ion-row v-if="formModel.cd4_available.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.cd4_available.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.cd4_available"
                    @ionChange="(e) => { formData.cd4_available = e.detail.value; dataHandler('cd4_available'); }">
                    <ion-item lines="none" v-for="option in formModel.cd4_available.options()" :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.cd4_available" color="danger">
                    {{ validationMessages.cd4_available }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- CD4 Percent -->
        <ion-row v-if="formModel.cd4_percent.required()">
            <ion-col size="12" size-md="6">
                <BasicInputField
                    :inputHeader="formModel.cd4_percent.label + (formModel.cd4_percent.required() ? '*' : '')"
                    placeholder="Enter CD4 percent" inputType="number" :inputValue="formData.cd4_percent"
                    @update:inputValue="(value) => { formData.cd4_percent = value; dataHandler('cd4_percent'); }"
                    :error="!!validationMessages.cd4_percent" unit="%" />
                <ion-note v-if="validationMessages.cd4_percent" color="danger">
                    {{ validationMessages.cd4_percent }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Confirmatory HIV Test Type -->
        <ion-row v-if="formModel.confirmatory_hiv_test_type.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_type.label }} <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.confirmatory_hiv_test_type"
                    @ionChange="(e) => { formData.confirmatory_hiv_test_type = e.detail.value; dataHandler('confirmatory_hiv_test_type'); }">
                    <ion-item lines="none" v-for="option in formModel.confirmatory_hiv_test_type.options()"
                        :key="option">
                        <ion-radio slot="start" :value="option"></ion-radio>
                        <ion-label>{{ option }}</ion-label>
                    </ion-item>
                </ion-radio-group>
                <ion-note v-if="validationMessages.confirmatory_hiv_test_type" color="danger">
                    {{ validationMessages.confirmatory_hiv_test_type }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Confirmatory HIV Test Location -->
        <ion-row v-if="formModel.confirmatory_hiv_test_location.required()">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">{{ formModel.confirmatory_hiv_test_location.label }} <span
                        style="color: red">*</span></ion-label>
                <SelectFacility :show_error="!!validationMessages.confirmatory_hiv_test_location"
                    :selected_district_ids="[]" :selected_location="formData.confirmatory_hiv_test_location"
                    @facilitySelected="(value: any) => { formData.confirmatory_hiv_test_location = value; dataHandler('confirmatory_hiv_test_location'); }" />
                <ion-note v-if="validationMessages.confirmatory_hiv_test_location" color="danger">
                    {{ validationMessages.confirmatory_hiv_test_location }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- Confirmatory HIV Test Date -->
        <ion-row v-if="formModel.confirmatory_hiv_test_date.required()">
            <ion-col size="12" size-md="6">
                <DatePicker
                    :place_holder="formModel.confirmatory_hiv_test_date.label + (formModel.confirmatory_hiv_test_date.required() ? '*' : '')"
                    :date_prop="formData.confirmatory_hiv_test_date"
                    @update:date="(value) => { formData.confirmatory_hiv_test_date = value; dataHandler('confirmatory_hiv_test_date'); }"
                    :error="!!validationMessages.confirmatory_hiv_test_date" />
                <ion-note v-if="validationMessages.confirmatory_hiv_test_date" color="danger">
                    {{ validationMessages.confirmatory_hiv_test_date }}
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- BMI Display -->
        <ion-row v-if="calculatedBMI">
            <ion-col size="12">
                <ion-card color="light">
                    <ion-card-content>
                        <ion-label>
                            <strong>BMI {{ calculatedBMI.value }}</strong>
                            <span :style="{ color: calculatedBMI.color }">{{ calculatedBMI.category }}</span>
                        </ion-label>
                    </ion-card-content>
                </ion-card>
            </ion-col>
        </ion-row>
    </ion-grid>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import { ARTClinicRegistrationService } from "../services/art_clinic_registration_service";
import { PatientService } from "@/services/patient_service";
import { validateScanFormLinkageCode } from "@/utils/Damm"
import { getFacilities } from "@/utils/HisFormHelpers/LocationFieldOptions";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import {
    IonGrid,
    IonRow,
    IonCol,
    IonLabel,
    IonRadioGroup,
    IonRadio,
    IonItem,
    IonNote,
    IonCard,
    IonCardContent
} from "@ionic/vue";

const patient = new PatientService()
const service = new ARTClinicRegistrationService(patient.getID(), -1);

const formData = ref<any>({
    phone_followup: '' as string,
    home_visit_followup: '' as string,
    has_linkage: '' as string,
    linkage_number: '' as string,
    received_arvs: '' as string,
    date_last_taken_arvs: '' as string,
    taken_last_two_months: '' as string,
    taken_last_two_weeks: '' as string,
    ever_registered_at_art_clinic: '' as string,
    location_of_art_initialization: '' as string,
    art_number_at_previous_location: '' as string,
    confirmatory_hiv_test_type: '' as string,
    confirmatory_hiv_test_location: '' as string,
    confirmatory_hiv_test_date: '' as string,
    has_transfer_letter: '' as string,
    art_registration_date: '' as string,
    art_start_date: '' as string,
    initial_weight: '' as string,
    initial_height: '' as string,
    cd4_available: '' as string,
    cd4_percent: '' as string
})

const facilities = ref<any>([])
const validationMessages = ref<any>({})

// BMI Calculation
const calculatedBMI = computed(() => {
    const weight = parseFloat(formData.value.initial_weight)
    const height = parseFloat(formData.value.initial_height)

    if (!weight || !height || weight <= 0 || height <= 0) {
        return null
    }

    // Convert height from cm to meters
    const heightInMeters = height / 100
    const bmi = weight / (heightInMeters * heightInMeters)
    const bmiValue = Math.round(bmi * 10) / 10

    let category = ''
    let color = ''

    if (bmiValue < 18.5) {
        category = 'Underweight'
        color = '#3880ff'
    } else if (bmiValue < 25) {
        category = 'Normal'
        color = '#2dd36f'
    } else if (bmiValue < 30) {
        category = 'Overweight'
        color = '#ffc409'
    } else {
        category = 'Obese'
        color = '#eb445a'
    }

    return {
        value: bmiValue,
        category,
        color
    }
})

const formModel: any = {
    followup: {
        required: () => true,
        label: 'Agrees to followup',
        buildObs: () => {
            return formModel.followup.options().map((o: any) => {
                return [
                    service.buildValueCoded(o.label, formData.value[o.id]),
                    service.buildValueCoded('Agrees to followup', o.label)
                ]
            }).flat(1)
        },
        options: () => {
            return [
                {
                    id: 'phone_followup',
                    label: 'Phone',
                    options: ['Yes', "No"]
                },
                {
                    id: 'home_visit_followup',
                    label: 'Home visit',
                    options: ['Yes', "No"]
                }
            ]
        },
        validation: () => {
            validationMessages.value['phone_followup'] = ''
            validationMessages.value['home_visit_followup'] = ''
            if (!formData.value.phone_followup) {
                validationMessages.value['phone_followup'] = 'Phone followup is required'
                return
            }
            if (!formData.value.home_visit_followup) {
                validationMessages.value['home_visit_followup'] = 'Home visit followup is required'
            }
        }
    },
    has_linkage: {
        required: () => true,
        label: 'Has linkage number',
        options: () => {
            return ['Yes', 'No']
        },
        validation: () => {
            validationMessages.value['has_linkage'] = ''
            if (!formData.value.has_linkage) {
                validationMessages.value['has_linkage'] = 'Has linkage is required'
            }
        },
        linkage_number: {
            required: () => formData.value.has_linkage === 'Yes',
            label: 'Linkage number',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_linkage' && value === 'Yes') {
                    formData.value.linkage_number = ''
                    validationMessages.value['linkage_number'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('HTC Serial number', formData.value.linkage_number)
            },
            validation: () => {
                validationMessages.value['linkage_number'] = ''
                const required = formModel.linkage_number.required()
                if (required && !formData.value.linkage_number) {
                    validationMessages.value['linkage_number'] = 'Linkage number is required'
                    return
                }
                if (required && !validateScanFormLinkageCode(formData.value.linkage_number)) {
                    validationMessages.value['linkage_number'] = 'Invalid linkage number'
                }
            }
        },
        received_arvs: {
            required: () => true,
            label: 'Received ARVs',
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Ever received ART', formData.value.received_arvs)
            },
            validation: () => {
                validationMessages.value['received_arvs'] = ''
                if (!formData.value.received_arvs) {
                    validationMessages.value['received_arvs'] = 'Received ARVs is required'
                }
            }
        },
        date_last_taken_arvs: {
            required: () => formData.value.received_arvs === 'Yes',
            label: 'Date last taken ARVs',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value === 'No') {
                    formData.value.date_last_taken_arvs = ''
                    validationMessages.value['date_last_taken_arvs'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueDate('Date ART last taken', formData.value.date_last_taken_arvs)
            },
            validation: () => {
                validationMessages.value['date_last_taken_arvs'] = ''
                const required = formModel.date_last_taken_arvs.required()
                if (required && !formData.value.date_last_taken_arvs) {
                    validationMessages.value['date_last_taken_arvs'] = 'Date last taken ARVs is required'
                }
            }
        },
        taken_last_two_months: {
            required: () => formData.value.received_arvs === 'Unknown',
            label: 'Taken last two months',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value == 'No') {
                    formData.value.taken_last_two_months = ''
                    validationMessages.value['taken_last_two_months'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No', 'Unknown']
            },
            buildObs: () => {
                return service.buildValueCoded('Has the patient taken ART in the last two months', formData.value.taken_last_two_months)
            },
            validation: () => {
                validationMessages.value['taken_last_two_months'] = ''
                const required = formModel.taken_last_two_months.required()
                if (required && !formData.value.taken_last_two_months) {
                    validationMessages.value['taken_last_two_months'] = 'Taken last two months is required'
                }
            }
        },
        taken_last_two_weeks: {
            required: () => formData.value.taken_last_two_months === 'Yes',
            label: 'Taken last two weeks',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'taken_last_two_months' && value === 'No') {
                    formData.value.taken_last_two_weeks = ''
                    validationMessages.value['taken_last_two_weeks'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No', 'Unknown']
            },
            buildObs: () => {
                return service.buildValueCoded('Has the patient taken ART in the last two weeks', formData.value.taken_last_two_weeks)
            },
            validation: () => {
                validationMessages.value['taken_last_two_weeks'] = ''
                const required = formModel.taken_last_two_weeks.required()
                if (required && !formData.value.taken_last_two_weeks) {
                    validationMessages.value['taken_last_two_weeks'] = 'Taken last two weeks is required'
                }
            }
        },
        ever_registered_at_art_clinic: {
            required: () => formData.value.received_arvs === 'Yes',
            label: 'Ever registered at ART clinic',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value === 'No') {
                    formData.value.ever_registered_at_art_clinic = ''
                    validationMessages.value['ever_registered_at_art_clinic'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Ever registered at ART clinic', formData.value.ever_registered_at_art_clinic)
            },
            validation: () => {
                validationMessages.value['ever_registered_at_art_clinic'] = ''
                const required = formModel.ever_registered_at_art_clinic.required()
                if (required && !formData.value.ever_registered_at_art_clinic) {
                    validationMessages.value['ever_registered_at_art_clinic'] = 'Ever registered at ART clinic is required'
                }
            }
        },
        location_of_art_initialization: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'Location of ART initialization',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.location_of_art_initialization = ''
                    validationMessages.value['location_of_art_initialization'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('Location of ART initialization', formData.value.location_of_art_initialization)
            },
            validation: () => {
                validationMessages.value['location_of_art_initialization'] = ''
                if (formData.value.ever_registered_at_art_clinic === 'Yes' && !formData.value.location_of_art_initialization) {
                    validationMessages.value['location_of_art_initialization'] = 'Location of ART initialization is required'
                }
            },
            searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res)
        },
        art_start_date: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'ART start date',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.art_start_date = ''
                    validationMessages.value['art_start_date'] = ''
                }
            },
            buildObs: () => {
                return buildDateObs('Date ART started', formData.value.art_start_date, false)
            },
            validation: () => {
                validationMessages.value['art_start_date'] = ''
                const required = formModel.art_start_date.required()
                if (required && !formData.value.art_start_date) {
                    validationMessages.value['art_start_date'] = 'ART start date is required'
                    return
                }
                if (required && new Date(formData.value.art_start_date) > new Date()) {
                    validationMessages.value['art_start_date'] = 'ART start date cannot be in the future'
                }
                if (required && new Date(formData.value.art_start_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['art_start_date'] = 'ART start date cannot be before birthdate'
                }
            }
        },
        art_number_at_previous_location: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'ART number at previous location',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.art_number_at_previous_location = ''
                    validationMessages.value['art_number_at_previous_location'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('ART number at previous location', formData.value.art_number_at_previous_location)
            },
            validation: () => {
                validationMessages.value['art_number_at_previous_location'] = ''
                const required = formModel.art_number_at_previous_location.required()
                if (required && !formData.value.art_number_at_previous_location) {
                    validationMessages.value['art_number_at_previous_location'] = 'ART number at previous location is required'
                }
            }
        },
        has_transfer_letter: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.has_transfer_letter = ''
                    validationMessages.value['has_transfer_letter'] = ''
                }
            },
            label: 'Has stage information?',
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Has transfer letter', formData.value.has_transfer_letter)
            },
            validation: () => {
                validationMessages.value['has_transfer_letter'] = ''
                const required = formModel.has_transfer_letter.required()
                if (required && !formData.value.has_transfer_letter) {
                    validationMessages.value['has_transfer_letter'] = 'Has transfer letter is required'
                }
            }
        },
        initial_height: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.initial_height = ''
                    validationMessages.value['initial_height'] = ''
                }
            },
            label: 'Initial height',
            buildObs: () => {
                return service.buildValueNumber('Height', formData.value.initial_height)
            },
            validation: () => {
                validationMessages.value['initial_height'] = ''
                const required = formModel.initial_height.required()
                if (required && !formData.value.initial_height) {
                    validationMessages.value['initial_height'] = 'Initial height is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.initial_height)) {
                    validationMessages.value['initial_height'] = 'Initial height must be a number'
                }
                if (required && formData.value.initial_height < 40) {
                    validationMessages.value['initial_height'] = 'Initial height cannot be less than 40'
                }
                if (required && formData.value.initial_height > 220) {
                    validationMessages.value['initial_height'] = 'Initial height cannot be greater than 220'
                }
            }
        },
        initial_weight: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            label: 'Initial weight',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.initial_weight = ''
                    validationMessages.value['initial_weight'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueNumber('Weight', formData.value.initial_weight)
            },
            validation: () => {
                validationMessages.value['initial_weight'] = ''
                const required = formModel.initial_weight.required()
                if (required && !formData.value.initial_weight) {
                    validationMessages.value['initial_weight'] = 'Initial weight is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.initial_weight)) {
                    validationMessages.value['initial_weight'] = 'Initial weight must be a number'
                }
                if (required && formData.value.initial_weight < 0.5) {
                    validationMessages.value['initial_weight'] = 'Initial weight cannot be less than 0.5'
                }
                if (required && formData.value.initial_weight > 250) {
                    validationMessages.value['initial_weight'] = 'Initial weight cannot be greater than 250'
                }
            }
        },
        cd4_available: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.cd4_available = ''
                    validationMessages.value['cd4_available'] = ''
                }
            },
            label: 'CD4 available',
            options: () => {
                return ['Yes', 'No']
            },
            validation: () => {
                validationMessages.value['cd4_available'] = ''
                if (!formData.value.cd4_available) {
                    validationMessages.value['cd4_available'] = 'CD4 available is required'
                }
            }
        },
        cd4_percent: {
            required: () => formData.value.cd4_available === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'cd4_available' && value === 'No') {
                    formData.value.cd4_percent = ''
                    validationMessages.value['cd4_percent'] = ''
                }
            },
            label: 'CD4 percent',
            buildObs: () => {
                return service.buildValueNumber(
                    'CD4 percent', parseInt(formData.value.cd4_percent.substring(1)), '%' as any
                )
            },
            validation: () => {
                validationMessages.value['cd4_percent'] = ''
                const required = formModel.cd4_percent.required()
                if (required && !formData.value.cd4_percent) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.cd4_percent)) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent must be a number'
                }
                if (required && formData.value.cd4_percent < 0) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent cannot be less than 0'
                }
                if (required && formData.value.cd4_percent > 100) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent cannot be greater than 100'
                }
            }
        },
        confirmatory_hiv_test_type: {
            required: () => true,
            label: 'Confirmatory hiv test type',
            options: () => ([
                { label: 'Rapid antibody test', value: 'HIV rapid test' },
                { label: 'DNA PCR', value: 'HIV DNA polymerase chain reaction' },
                { label: 'Not done', value: 'Not done', disabled: formData.value.has_linkage === 'Yes' }
            ]),
            buildObs: () => {
                return service.buildValueCoded('Confirmatory hiv test type', formData.value.confirmatory_hiv_test_type)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_type'] = ''
                if (!formData.value.confirmatory_hiv_test_type) {
                    validationMessages.value['confirmatory_hiv_test_type'] = 'Confirmatory hiv test type is required'
                }
            }
        },
        confirmatory_hiv_test_location: {
            required: () => formData.value.confirmatory_hiv_test_type !== 'Not done',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                    formData.value.confirmatory_hiv_test_location = ''
                    validationMessages.value['confirmatory_hiv_test_location'] = ''
                }
            },
            label: 'Confirmatory hiv test location',
            buildObs: () => {
                return service.buildValueText('Confirmatory hiv test location', formData.value.confirmatory_hiv_test_location)
            },
            searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res),
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_location'] = ''
                if (!formData.value.confirmatory_hiv_test_location) {
                    validationMessages.value['confirmatory_hiv_test_location'] = 'Confirmatory hiv test location is required'
                }
            }
        },
        confirmatory_hiv_test_date: {
            required: () => formData.value.confirmatory_hiv_test_type !== 'Not done',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                    formData.value.confirmatory_hiv_test_date = ''
                    validationMessages.value['confirmatory_hiv_test_date'] = ''
                }
            },
            label: 'Confirmatory hiv test date',
            buildObs: () => {
                return buildDateObs('Confirmatory hiv test date', formData.value.confirmatory_hiv_test_date, false)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_date'] = ''
                const required = formModel.confirmatory_hiv_test_date.required()
                if (required && !formData.value.confirmatory_hiv_test_date) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date is required'
                    return
                }
                if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) > new Date()) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be in the future'
                }
                if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be before birthdate'
                }
            }
        },
        art_registration_date: {
            required: () => true,
            label: 'ART Registration Date',
            buildObs: () => {
                return buildDateObs('ART Registration Date', formData.value.art_registration_date, false)
            },
            validation: () => {
                validationMessages.value['art_registration_date'] = ''
                const required = formModel.art_registration_date.required()
                if (required && !formData.value.art_registration_date) {
                    validationMessages.value['art_registration_date'] = 'ART Registration Date is required'
                    return
                }
                if (required && new Date(formData.value.art_registration_date) > new Date()) {
                    validationMessages.value['art_registration_date'] = 'ART Registration Date cannot be in the future'
                }
                if (required && new Date(formData.value.art_registration_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['art_registration_date'] = 'ART Registration Date cannot be before birthdate'
                }
            }
        },
        confirmatory_hiv_test_type: {
            required: () => true,
            label: 'Confirmatory HIV Test Type',
            options: () => {
                return ['Rapid Test', 'ELISA', 'Western Blot', 'PCR']
            },
            buildObs: () => {
                return service.buildValueCoded('Confirmatory HIV Test Type', formData.value.confirmatory_hiv_test_type)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_type'] = ''
                const required = formModel.confirmatory_hiv_test_type.required()
                if (required && !formData.value.confirmatory_hiv_test_type) {
                    validationMessages.value['confirmatory_hiv_test_type'] = 'Confirmatory HIV Test Type is required'
                }
            }
        },
        confirmatory_hiv_test_location: {
            required: () => true,
            label: 'Confirmatory HIV Test Location',
            buildObs: () => {
                return service.buildValueText('Confirmatory HIV Test Location', formData.value.confirmatory_hiv_test_location)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_location'] = ''
                const required = formModel.confirmatory_hiv_test_location.required()
                if (required && !formData.value.confirmatory_hiv_test_location) {
                    validationMessages.value['confirmatory_hiv_test_location'] = 'Confirmatory HIV Test Location is required'
                }
            }
        }
    }
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function dataHandler(field: string) {
    runValidation(field)
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, formData.value[k]))
}



function buildDateObs(conceptName: string, date: string, isEstimate: boolean) {
    let obs = {}
    if (date.match(/unknown/i)) {
        obs = service.buildValueText(conceptName, 'Unknown')
    } else if (isEstimate) {
        obs = service.buildValueDateEstimated(conceptName, date)
    } else {
        obs = service.buildValueDate(conceptName, date)
    }
    return obs
}

function runValidation(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].required === 'function') {
        const required = formModel[field].required()
        if (required && field in formData.value && !formData.value[field]) {
            validationMessages.value[field] = 'This field is required'
            return
        }
    }
    if (typeof formModel[field].validation === 'function') {
        formModel[field].validation()
    }
}

function validateAll() {
    Object.keys(formModel).forEach((key: string) => runValidation(key))
    return Object.keys(validationMessages.value).every((key) => `${validationMessages.value[key]}`.length <= 0)
}

async function onSubmit() {
    if (!validateAll()) {
        toastWarning("Please review form for errors")
        return false
    }
    try {
        await service.createEncounter()
        const obs = await Promise.all(buildObs())
        await service.onSubmit(obs)
        toastSuccess("ART Clinic Registration saved successfully")
        return true
    } catch (e) {
        console.error(e)
        toastDanger("Error has occured while saving observations")
        return false
    }
}

defineExpose({
    onSubmit
})
</script>

<style scoped>
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-label span {
    color: #eb445a;
}

ion-radio-group {
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin-top: 8px;
}

ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
}

ion-radio {
    margin-right: 8px;
}

ion-card {
    margin: 16px 0;
}

ion-note {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: block;
}

.error-state {
    border-color: #eb445a !important;
}
</style>