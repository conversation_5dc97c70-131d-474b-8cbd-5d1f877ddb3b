<template>
    <ion-grid>
        <!-- Error Messages -->
        <ion-row v-if="Object.keys(validationMessages).some(key => validationMessages[key])">
            <ion-col size="12">
                <ion-note color="danger">
                    <div v-for="(message, field) in validationMessages" :key="field">
                        <div v-if="message">{{ message }}</div>
                    </div>
                </ion-note>
            </ion-col>
        </ion-row>

        <!-- ARV Number and Registration Date -->
        <ion-row>
            <ion-col size="12" size-md="6">
                <BasicInputField :inputHeader="'ARV Number*'" placeholder="e.g 192"
                    :inputValue="formData.linkage_number"
                    @update:inputValue="(value) => { formData.linkage_number = value; dataHandler('linkage_number'); }"
                    :error="!!validationMessages.linkage_number">
                    <template #end>
                        <ion-label>{{ 'set_site_prefix' }}</ion-label>
                    </template>
                </BasicInputField>
            </ion-col>
            <ion-col size="12" size-md="6">
                <DatePicker :place_holder="'Registration Date*'" :date_prop="formData.art_registration_date"
                    @update:date="(value) => { formData.art_registration_date = value; dataHandler('art_registration_date'); }"
                    :error="!!validationMessages.art_registration_date" />
            </ion-col>
        </ion-row>

        <!-- Agrees to follow up and Ever received ARVs -->
        <ion-row>
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">Agrees to follow up? <span style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.phone_followup"
                    @ionChange="(e) => { formData.phone_followup = e.detail.value; dataHandler('phone_followup'); }">
                    <ion-item lines="none">
                        <ion-radio slot="start" value="Yes"></ion-radio>
                        <ion-label>Yes</ion-label>
                    </ion-item>
                    <ion-item lines="none">
                        <ion-radio slot="start" value="No"></ion-radio>
                        <ion-label>No</ion-label>
                    </ion-item>
                </ion-radio-group>
            </ion-col>
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">Ever received ARVs for treatment or prophylaxis? <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.received_arvs"
                    @ionChange="(e) => { formData.received_arvs = e.detail.value; dataHandler('received_arvs'); }">
                    <ion-item lines="none">
                        <ion-radio slot="start" value="Yes"></ion-radio>
                        <ion-label>Yes</ion-label>
                    </ion-item>
                    <ion-item lines="none">
                        <ion-radio slot="start" value="No"></ion-radio>
                        <ion-label>No</ion-label>
                    </ion-item>
                </ion-radio-group>
            </ion-col>
        </ion-row>

        <!-- Date last taken ARVs -->
        <ion-row v-if="formData.received_arvs === 'Yes'">
            <ion-col size="12" size-md="6">
                <DatePicker :place_holder="'Date last taken ARVs'" :date_prop="formData.date_last_taken_arvs"
                    @update:date="(value) => { formData.date_last_taken_arvs = value; dataHandler('date_last_taken_arvs'); }"
                    :error="!!validationMessages.date_last_taken_arvs" />
            </ion-col>
        </ion-row>

        <!-- Ever registered at ART clinic -->
        <ion-row>
            <ion-col size="12">
                <ion-label class="form-label">Ever registered at an ART clinic: <span
                        style="color: red">*</span></ion-label>
                <ion-radio-group :value="formData.ever_registered_at_art_clinic"
                    @ionChange="(e) => { formData.ever_registered_at_art_clinic = e.detail.value; dataHandler('ever_registered_at_art_clinic'); }">
                    <ion-item lines="none">
                        <ion-radio slot="start" value="Yes"></ion-radio>
                        <ion-label>Yes</ion-label>
                    </ion-item>
                    <ion-item lines="none">
                        <ion-radio slot="start" value="No"></ion-radio>
                        <ion-label>No</ion-label>
                    </ion-item>
                </ion-radio-group>
            </ion-col>
        </ion-row>

        <!-- Location of ART Initiation and ART Number at Previous Location -->
        <ion-row v-if="formData.ever_registered_at_art_clinic === 'Yes'">
            <ion-col size="12" size-md="6">
                <ion-label class="form-label">Location of ART Initiation</ion-label>
                <SelectFacility :show_error="!!validationMessages.location_of_art_initialization"
                    :selected_district_ids="[]" :selected_location="formData.location_of_art_initialization"
                    @facilitySelected="(value: any) => { formData.location_of_art_initialization = value; dataHandler('location_of_art_initialization'); }" />
            </ion-col>
            <ion-col size="12" size-md="6">
                <BasicInputField :inputHeader="'ART Number at Previous Location'"
                    placeholder="Enter previous ART number" :inputValue="formData.art_number_at_previous_location"
                    @update:inputValue="(value) => { formData.art_number_at_previous_location = value; dataHandler('art_number_at_previous_location'); }"
                    :error="!!validationMessages.art_number_at_previous_location" />
            </ion-col>
        </ion-row>

        <!-- Date started ART, Initial Weight, Initial Height -->
        <ion-row>
            <ion-col size="12" size-md="4">
                <DatePicker :place_holder="'Date started ART'" :date_prop="formData.art_start_date"
                    @update:date="(value) => { formData.art_start_date = value; dataHandler('art_start_date'); }"
                    :error="!!validationMessages.art_start_date" />
            </ion-col>
            <ion-col size="12" size-md="4">
                <BasicInputField :inputHeader="'Initial Weight'" placeholder="1" inputType="number"
                    :inputValue="formData.initial_weight"
                    @update:inputValue="(value) => { formData.initial_weight = value; dataHandler('initial_weight'); calculateBMI(); }"
                    :error="!!validationMessages.initial_weight" unit="KG" />
            </ion-col>
            <ion-col size="12" size-md="4">
                <BasicInputField :inputHeader="'Initial Height'" placeholder="1" inputType="number"
                    :inputValue="formData.initial_height"
                    @update:inputValue="(value) => { formData.initial_height = value; dataHandler('initial_height'); calculateBMI(); }"
                    :error="!!validationMessages.initial_height" unit="CM" />
            </ion-col>
        </ion-row>

        <!-- BMI Display -->
        <ion-row v-if="calculatedBMI">
            <ion-col size="12">
                <ion-card color="light">
                    <ion-card-content>
                        <ion-label>
                            <strong>BMI {{ calculatedBMI.value }}</strong>
                            <span :style="{ color: calculatedBMI.color }">{{ calculatedBMI.category }}</span>
                        </ion-label>
                    </ion-card-content>
                </ion-card>
            </ion-col>
        </ion-row>
    </ion-grid>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import { ARTClinicRegistrationService } from "../services/art_clinic_registration_service";
import { PatientService } from "@/services/patient_service";
import { validateScanFormLinkageCode } from "@/utils/Damm"
import { getFacilities } from "@/utils/HisFormHelpers/LocationFieldOptions";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";
import BasicInputField from "@/components/BasicInputField.vue";
import DatePicker from "@/components/DatePicker.vue";
import SelectFacility from "@/apps/OPD/components/SelectFacility.vue";
import {
    IonGrid,
    IonRow,
    IonCol,
    IonLabel,
    IonRadioGroup,
    IonRadio,
    IonItem,
    IonNote,
    IonCard,
    IonCardContent
} from "@ionic/vue";

const patient = new PatientService()
const service = new ARTClinicRegistrationService(patient.getID(), -1);

const formData = ref<any>({
    phone_followup: '' as string,
    home_visit_followup: '' as string,
    has_linkage: '' as string,
    linkage_number: '' as string,
    received_arvs: '' as string,
    date_last_taken_arvs: '' as string,
    taken_last_two_months: '' as string,
    taken_last_two_weeks: '' as string,
    ever_registered_at_art_clinic: '' as string,
    location_of_art_initialization: '' as string,
    art_number_at_previous_location: '' as string,
    confirmatory_hiv_test_type: '' as string,
    confirmatory_hiv_test_location: '' as string,
    confirmatory_hiv_test_date: '' as string,
    has_transfer_letter: '' as string,
    art_registration_date: '' as string,
    art_start_date: '' as string,
    initial_weight: '' as string,
    initial_height: '' as string,
    cd4_available: '' as string,
    cd4_percent: '' as string
})

const facilities = ref<any>([])
const validationMessages = ref<any>({})

// BMI Calculation
const calculatedBMI = computed(() => {
    const weight = parseFloat(formData.value.initial_weight)
    const height = parseFloat(formData.value.initial_height)

    if (!weight || !height || weight <= 0 || height <= 0) {
        return null
    }

    // Convert height from cm to meters
    const heightInMeters = height / 100
    const bmi = weight / (heightInMeters * heightInMeters)
    const bmiValue = Math.round(bmi * 10) / 10

    let category = ''
    let color = ''

    if (bmiValue < 18.5) {
        category = 'Underweight'
        color = '#3880ff'
    } else if (bmiValue < 25) {
        category = 'Normal'
        color = '#2dd36f'
    } else if (bmiValue < 30) {
        category = 'Overweight'
        color = '#ffc409'
    } else {
        category = 'Obese'
        color = '#eb445a'
    }

    return {
        value: bmiValue,
        category,
        color
    }
})

const formModel: any = {
    followup: {
        required: () => true,
        label: 'Agrees to followup',
        buildObs: () => {
            return formModel.followup.options().map((o: any) => {
                return [
                    service.buildValueCoded(o.label, formData.value[o.id]),
                    service.buildValueCoded('Agrees to followup', o.label)
                ]
            }).flat(1)
        },
        options: () => {
            return [
                {
                    id: 'phone_followup',
                    label: 'Phone',
                    options: ['Yes', "No"]
                },
                {
                    id: 'home_visit_followup',
                    label: 'Home visit',
                    options: ['Yes', "No"]
                }
            ]
        },
        validation: () => {
            validationMessages.value['phone_followup'] = ''
            validationMessages.value['home_visit_followup'] = ''
            if (!formData.value.phone_followup) {
                validationMessages.value['phone_followup'] = 'Phone followup is required'
                return
            }
            if (!formData.value.home_visit_followup) {
                validationMessages.value['home_visit_followup'] = 'Home visit followup is required'
            }
        }
    },
    has_linkage: {
        required: () => true,
        label: 'Has linkage number',
        options: () => {
            return ['Yes', 'No']
        },
        validation: () => {
            validationMessages.value['has_linkage'] = ''
            if (!formData.value.has_linkage) {
                validationMessages.value['has_linkage'] = 'Has linkage is required'
            }
        },
        linkage_number: {
            required: () => formData.value.has_linkage === 'Yes',
            label: 'Linkage number',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_linkage' && value === 'Yes') {
                    formData.value.linkage_number = ''
                    validationMessages.value['linkage_number'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('HTC Serial number', formData.value.linkage_number)
            },
            validation: () => {
                validationMessages.value['linkage_number'] = ''
                const required = formModel.linkage_number.required()
                if (required && !formData.value.linkage_number) {
                    validationMessages.value['linkage_number'] = 'Linkage number is required'
                    return
                }
                if (required && !validateScanFormLinkageCode(formData.value.linkage_number)) {
                    validationMessages.value['linkage_number'] = 'Invalid linkage number'
                }
            }
        },
        received_arvs: {
            required: () => true,
            label: 'Received ARVs',
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Ever received ART', formData.value.received_arvs)
            },
            validation: () => {
                validationMessages.value['received_arvs'] = ''
                if (!formData.value.received_arvs) {
                    validationMessages.value['received_arvs'] = 'Received ARVs is required'
                }
            }
        },
        date_last_taken_arvs: {
            required: () => formData.value.received_arvs === 'Yes',
            label: 'Date last taken ARVs',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value === 'No') {
                    formData.value.date_last_taken_arvs = ''
                    validationMessages.value['date_last_taken_arvs'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueDate('Date ART last taken', formData.value.date_last_taken_arvs)
            },
            validation: () => {
                validationMessages.value['date_last_taken_arvs'] = ''
                const required = formModel.date_last_taken_arvs.required()
                if (required && !formData.value.date_last_taken_arvs) {
                    validationMessages.value['date_last_taken_arvs'] = 'Date last taken ARVs is required'
                }
            }
        },
        taken_last_two_months: {
            required: () => formData.value.received_arvs === 'Unknown',
            label: 'Taken last two months',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value == 'No') {
                    formData.value.taken_last_two_months = ''
                    validationMessages.value['taken_last_two_months'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No', 'Unknown']
            },
            buildObs: () => {
                return service.buildValueCoded('Has the patient taken ART in the last two months', formData.value.taken_last_two_months)
            },
            validation: () => {
                validationMessages.value['taken_last_two_months'] = ''
                const required = formModel.taken_last_two_months.required()
                if (required && !formData.value.taken_last_two_months) {
                    validationMessages.value['taken_last_two_months'] = 'Taken last two months is required'
                }
            }
        },
        taken_last_two_weeks: {
            required: () => formData.value.taken_last_two_months === 'Yes',
            label: 'Taken last two weeks',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'taken_last_two_months' && value === 'No') {
                    formData.value.taken_last_two_weeks = ''
                    validationMessages.value['taken_last_two_weeks'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No', 'Unknown']
            },
            buildObs: () => {
                return service.buildValueCoded('Has the patient taken ART in the last two weeks', formData.value.taken_last_two_weeks)
            },
            validation: () => {
                validationMessages.value['taken_last_two_weeks'] = ''
                const required = formModel.taken_last_two_weeks.required()
                if (required && !formData.value.taken_last_two_weeks) {
                    validationMessages.value['taken_last_two_weeks'] = 'Taken last two weeks is required'
                }
            }
        },
        ever_registered_at_art_clinic: {
            required: () => formData.value.received_arvs === 'Yes',
            label: 'Ever registered at ART clinic',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value === 'No') {
                    formData.value.ever_registered_at_art_clinic = ''
                    validationMessages.value['ever_registered_at_art_clinic'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Ever registered at ART clinic', formData.value.ever_registered_at_art_clinic)
            },
            validation: () => {
                validationMessages.value['ever_registered_at_art_clinic'] = ''
                const required = formModel.ever_registered_at_art_clinic.required()
                if (required && !formData.value.ever_registered_at_art_clinic) {
                    validationMessages.value['ever_registered_at_art_clinic'] = 'Ever registered at ART clinic is required'
                }
            }
        },
        location_of_art_initialization: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'Location of ART initialization',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.location_of_art_initialization = ''
                    validationMessages.value['location_of_art_initialization'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('Location of ART initialization', formData.value.location_of_art_initialization)
            },
            validation: () => {
                validationMessages.value['location_of_art_initialization'] = ''
                if (formData.value.ever_registered_at_art_clinic === 'Yes' && !formData.value.location_of_art_initialization) {
                    validationMessages.value['location_of_art_initialization'] = 'Location of ART initialization is required'
                }
            },
            searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res)
        },
        art_start_date: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'ART start date',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.art_start_date = ''
                    validationMessages.value['art_start_date'] = ''
                }
            },
            buildObs: () => {
                return buildDateObs('Date ART started', formData.value.art_start_date, false)
            },
            validation: () => {
                validationMessages.value['art_start_date'] = ''
                const required = formModel.art_start_date.required()
                if (required && !formData.value.art_start_date) {
                    validationMessages.value['art_start_date'] = 'ART start date is required'
                    return
                }
                if (required && new Date(formData.value.art_start_date) > new Date()) {
                    validationMessages.value['art_start_date'] = 'ART start date cannot be in the future'
                }
                if (required && new Date(formData.value.art_start_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['art_start_date'] = 'ART start date cannot be before birthdate'
                }
            }
        },
        art_number_at_previous_location: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'ART number at previous location',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.art_number_at_previous_location = ''
                    validationMessages.value['art_number_at_previous_location'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('ART number at previous location', formData.value.art_number_at_previous_location)
            },
            validation: () => {
                validationMessages.value['art_number_at_previous_location'] = ''
                const required = formModel.art_number_at_previous_location.required()
                if (required && !formData.value.art_number_at_previous_location) {
                    validationMessages.value['art_number_at_previous_location'] = 'ART number at previous location is required'
                }
            }
        },
        has_transfer_letter: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.has_transfer_letter = ''
                    validationMessages.value['has_transfer_letter'] = ''
                }
            },
            label: 'Has stage information?',
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Has transfer letter', formData.value.has_transfer_letter)
            },
            validation: () => {
                validationMessages.value['has_transfer_letter'] = ''
                const required = formModel.has_transfer_letter.required()
                if (required && !formData.value.has_transfer_letter) {
                    validationMessages.value['has_transfer_letter'] = 'Has transfer letter is required'
                }
            }
        },
        initial_height: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.initial_height = ''
                    validationMessages.value['initial_height'] = ''
                }
            },
            label: 'Initial height',
            buildObs: () => {
                return service.buildValueNumber('Height', formData.value.initial_height)
            },
            validation: () => {
                validationMessages.value['initial_height'] = ''
                const required = formModel.initial_height.required()
                if (required && !formData.value.initial_height) {
                    validationMessages.value['initial_height'] = 'Initial height is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.initial_height)) {
                    validationMessages.value['initial_height'] = 'Initial height must be a number'
                }
                if (required && formData.value.initial_height < 40) {
                    validationMessages.value['initial_height'] = 'Initial height cannot be less than 40'
                }
                if (required && formData.value.initial_height > 220) {
                    validationMessages.value['initial_height'] = 'Initial height cannot be greater than 220'
                }
            }
        },
        initial_weight: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            label: 'Initial weight',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.initial_weight = ''
                    validationMessages.value['initial_weight'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueNumber('Weight', formData.value.initial_weight)
            },
            validation: () => {
                validationMessages.value['initial_weight'] = ''
                const required = formModel.initial_weight.required()
                if (required && !formData.value.initial_weight) {
                    validationMessages.value['initial_weight'] = 'Initial weight is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.initial_weight)) {
                    validationMessages.value['initial_weight'] = 'Initial weight must be a number'
                }
                if (required && formData.value.initial_weight < 0.5) {
                    validationMessages.value['initial_weight'] = 'Initial weight cannot be less than 0.5'
                }
                if (required && formData.value.initial_weight > 250) {
                    validationMessages.value['initial_weight'] = 'Initial weight cannot be greater than 250'
                }
            }
        },
        cd4_available: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.cd4_available = ''
                    validationMessages.value['cd4_available'] = ''
                }
            },
            label: 'CD4 available',
            options: () => {
                return ['Yes', 'No']
            },
            validation: () => {
                validationMessages.value['cd4_available'] = ''
                if (!formData.value.cd4_available) {
                    validationMessages.value['cd4_available'] = 'CD4 available is required'
                }
            }
        },
        cd4_percent: {
            required: () => formData.value.cd4_available === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'cd4_available' && value === 'No') {
                    formData.value.cd4_percent = ''
                    validationMessages.value['cd4_percent'] = ''
                }
            },
            label: 'CD4 percent',
            buildObs: () => {
                return service.buildValueNumber(
                    'CD4 percent', parseInt(formData.value.cd4_percent.substring(1)), '%' as any
                )
            },
            validation: () => {
                validationMessages.value['cd4_percent'] = ''
                const required = formModel.cd4_percent.required()
                if (required && !formData.value.cd4_percent) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.cd4_percent)) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent must be a number'
                }
                if (required && formData.value.cd4_percent < 0) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent cannot be less than 0'
                }
                if (required && formData.value.cd4_percent > 100) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent cannot be greater than 100'
                }
            }
        },
        confirmatory_hiv_test_type: {
            required: () => true,
            label: 'Confirmatory hiv test type',
            options: () => ([
                { label: 'Rapid antibody test', value: 'HIV rapid test' },
                { label: 'DNA PCR', value: 'HIV DNA polymerase chain reaction' },
                { label: 'Not done', value: 'Not done', disabled: formData.value.has_linkage === 'Yes' }
            ]),
            buildObs: () => {
                return service.buildValueCoded('Confirmatory hiv test type', formData.value.confirmatory_hiv_test_type)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_type'] = ''
                if (!formData.value.confirmatory_hiv_test_type) {
                    validationMessages.value['confirmatory_hiv_test_type'] = 'Confirmatory hiv test type is required'
                }
            }
        },
        confirmatory_hiv_test_location: {
            required: () => formData.value.confirmatory_hiv_test_type !== 'Not done',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                    formData.value.confirmatory_hiv_test_location = ''
                    validationMessages.value['confirmatory_hiv_test_location'] = ''
                }
            },
            label: 'Confirmatory hiv test location',
            buildObs: () => {
                return service.buildValueText('Confirmatory hiv test location', formData.value.confirmatory_hiv_test_location)
            },
            searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res),
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_location'] = ''
                if (!formData.value.confirmatory_hiv_test_location) {
                    validationMessages.value['confirmatory_hiv_test_location'] = 'Confirmatory hiv test location is required'
                }
            }
        },
        confirmatory_hiv_test_date: {
            required: () => formData.value.confirmatory_hiv_test_type !== 'Not done',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                    formData.value.confirmatory_hiv_test_date = ''
                    validationMessages.value['confirmatory_hiv_test_date'] = ''
                }
            },
            label: 'Confirmatory hiv test date',
            buildObs: () => {
                return buildDateObs('Confirmatory hiv test date', formData.value.confirmatory_hiv_test_date, false)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_date'] = ''
                const required = formModel.confirmatory_hiv_test_date.required()
                if (required && !formData.value.confirmatory_hiv_test_date) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date is required'
                    return
                }
                if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) > new Date()) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be in the future'
                }
                if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be before birthdate'
                }
            }
        }
    }
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function dataHandler(field: string) {
    runValidation(field)
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, formData.value[k]))
}

function calculateBMI() {
    // BMI calculation is handled by the computed property
    // This function is called to trigger reactivity
}

function buildDateObs(conceptName: string, date: string, isEstimate: boolean) {
    let obs = {}
    if (date.match(/unknown/i)) {
        obs = service.buildValueText(conceptName, 'Unknown')
    } else if (isEstimate) {
        obs = service.buildValueDateEstimated(conceptName, date)
    } else {
        obs = service.buildValueDate(conceptName, date)
    }
    return obs
}

function runValidation(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].required === 'function') {
        const required = formModel[field].required()
        if (required && field in formData.value && !formData.value[field]) {
            validationMessages.value[field] = 'This field is required'
            return
        }
    }
    if (typeof formModel[field].validation === 'function') {
        formModel[field].validation()
    }
}

function validateAll() {
    Object.keys(formModel).forEach((key: string) => runValidation(key))
    return Object.keys(validationMessages.value).every((key) => `${validationMessages.value[key]}`.length <= 0)
}

async function onSubmit() {
    if (!validateAll()) {
        toastWarning("Please review form for errors")
        return false
    }
    try {
        await service.createEncounter()
        const obs = await Promise.all(buildObs())
        await service.onSubmit(obs)
        toastSuccess("ART Clinic Registration saved successfully")
        return true
    } catch (e) {
        console.error(e)
        toastDanger("Error has occured while saving observations")
        return false
    }
}

defineExpose({
    onSubmit
})
</script>

<style scoped>
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
}

.form-label span {
    color: #eb445a;
}

ion-radio-group {
    display: flex;
    flex-direction: row;
    gap: 16px;
    margin-top: 8px;
}

ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
}

ion-radio {
    margin-right: 8px;
}

ion-card {
    margin: 16px 0;
}

ion-note {
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    display: block;
}

.error-state {
    border-color: #eb445a !important;
}
</style>