import { useWorkerStore } from "@/stores/workerStore";
import { useDemographicsStore } from "@/stores/DemographicStore";
import { useStatusStore } from "@/stores/StatusStore";
import { Service } from "@/services/service";
// IndexedDB Helper Functions for MaHis Database

const DB_NAME = "MaHis";
const DB_VERSION = 12;

/**
 * Open or create the IndexedDB database connection
 * @param storeName - Name of the object store to create/use
 * @param keyPath - Key path for the object store
 * @returns Promise resolving to IDBDatabase
 */
function openDatabase(storeName: string = "defaultStore", keyPath: string = "id"): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open(DB_NAME, DB_VERSION);

        request.onupgradeneeded = (event) => {
            const db = (event.target as IDBOpenDBRequest).result;

            // Create object store if it doesn't exist
            if (!db.objectStoreNames.contains(storeName)) {
                db.createObjectStore(storeName, { keyPath });
            }
        };

        request.onsuccess = (event) => {
            resolve((event.target as IDBOpenDBRequest).result);
        };

        request.onerror = (event) => {
            reject(`IndexedDB error: ${(event.target as IDBOpenDBRequest).error}`);
        };
    });
}

/**
 * Retrieve paginated data from the database
 * @param storeName - Name of the object store
 * @param options - Pagination and filtering options
 * @returns Promise resolving to paginated records
 */
export async function getOfflineRecords<T = any>(
    storeName: string,
    options: {
        currentPage?: number;
        itemsPerPage?: number;
        whereClause?: Partial<T>;
        likeClause?: any;
        inClause?: {
            [K in keyof Partial<T>]?: any[];
        };
        groupBy?: any;
        sortBy?: keyof T;
        sortOrder?: "asc" | "desc";
    } = {}
): Promise<{ records: T[]; totalCount: number } | T[]> {
    const { currentPage = 1, itemsPerPage = 0, whereClause, likeClause, inClause, groupBy, sortBy, sortOrder = "asc" } = options;
    const db = await openDatabase(storeName);
    if (!(db.objectStoreNames.length > 0)) return [];
    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], "readonly");
        const objectStore = transaction.objectStore(storeName);
        const request = objectStore.getAll();

        request.onsuccess = (event) => {
            let allRecords = (event.target as IDBRequest).result as T[];

            // Apply where clause filtering if provided
            let filteredRecords = whereClause
                ? allRecords.filter((record) =>
                      Object.entries(whereClause).every(([path, value]) => {
                          const recordValue = path.includes(".")
                              ? path.split(".").reduce((obj: any, key) => obj?.[key], record)
                              : (record as any)[path];

                          return typeof value === "string" ? String(recordValue).toLowerCase() === value.toLowerCase() : recordValue === value;
                      })
                  )
                : allRecords;

            // Apply LIKE clause filtering if provided
            if (likeClause) {
                filteredRecords = filteredRecords.filter((record) =>
                    Object.entries(likeClause).every(([path, pattern]) => {
                        if (typeof pattern !== "string") return false;

                        // Handle both nested and non-nested paths
                        const value = path.includes(".") ? path.split(".").reduce((obj: any, key) => obj?.[key], record) : (record as any)[path];

                        if (!value) return false;

                        const recordValue = String(value).toLowerCase();
                        const regexPattern = pattern.toLowerCase().replace(/%/g, ".*").replace(/_/g, ".");
                        const regex = new RegExp(`^${regexPattern}$`);
                        return regex.test(recordValue);
                    })
                );
            }

            // Apply IN clause filtering if provided
            if (inClause) {
                filteredRecords = filteredRecords.filter((record) =>
                    Object.entries(inClause).every(([path, values]: any) => {
                        const recordValue = path.includes(".")
                            ? path.split(".").reduce((obj: any, key: any) => obj?.[key], record)
                            : (record as any)[path];

                        return values.some((value: any) =>
                            typeof value === "string" ? String(recordValue).toLowerCase() === value.toLowerCase() : recordValue === value
                        );
                    })
                );
            }

            if (groupBy) {
                // Configuration option for getting first or last record (default: last)
                const groupByOption = "last"; // 'first' or 'last'

                // Group by the specified field and keep only first or last record of each group
                const groupedById = new Map();
                filteredRecords.forEach((record: any) => {
                    const groupValue = groupBy.includes(".")
                        ? groupBy.split(".").reduce((obj: any, key: any) => obj?.[key], record)
                        : record[groupBy];

                    const key = String(groupValue ?? "undefined"); // Handle null/undefined values

                    if (!groupedById.has(key)) {
                        // First record for this group
                        groupedById.set(key, record);
                    } else if (groupByOption === "last") {
                        // Replace with the latest record if we want the last one
                        groupedById.set(key, record);
                    }
                    // For 'first' option, we don't replace the existing record
                });

                // Convert map values back to array
                filteredRecords = Array.from(groupedById.values());
            }
            // Sort records if sortBy is provided
            if (sortBy) {
                filteredRecords.sort((a, b) => {
                    const valueA = a[sortBy];
                    const valueB = b[sortBy];
                    if (valueA < valueB) return sortOrder === "asc" ? -1 : 1;
                    if (valueA > valueB) return sortOrder === "asc" ? 1 : -1;
                    return 0;
                });
            }

            // Calculate total count before pagination
            const totalCount = filteredRecords.length;

            // Calculate start and end indices based on currentPage and itemsPerPage
            if (itemsPerPage != 0) {
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = startIndex + itemsPerPage;
                // Apply pagination
                const paginatedRecords = filteredRecords.slice(startIndex, endIndex);
                resolve({
                    records: paginatedRecords,
                    totalCount,
                });
            }

            resolve(filteredRecords);
        };

        request.onerror = (event) => {
            reject(`Error retrieving paginated data: ${(event.target as IDBRequest).error}`);
        };
    });
}
export async function getOfflineFirstObsValue(data: any, value_type: string, concept_id?: number): Promise<string | number | undefined> {
    // If concept_id is provided, filter the data first
    const filteredData = concept_id ? data.filter((item: any) => item.concept_id === concept_id) : data;

    // Then sort and return the first item's specified value
    return filteredData.sort((a: any, b: any) => new Date(b.obs_datetime).getTime() - new Date(a.obs_datetime).getTime())[0]?.[value_type];
}
export function getOfflineSavedUnsavedData(element: string) {
    const data = useDemographicsStore();
    const patientRecord = data.patient;
    return [...(patientRecord[element]?.saved || []), ...(patientRecord[element]?.unsaved || [])];
}
export function getObjectsWithLatestDate(data: any) {
    // Guard clause for empty arrays
    if (!data || data.length === 0) {
        return [];
    }

    // Find the latest date in the dataset
    const latestDate = data.reduce((latest: any, currentObj: any) => {
        const currentDate = new Date(currentObj.obs_datetime);
        return currentDate > latest ? currentDate : latest;
    }, new Date(data[0]?.obs_datetime));

    // Filter objects to only include those with the latest date
    const latestObjects = data.filter((obj: any) => {
        const objDate = new Date(obj.obs_datetime);
        return objDate.getTime() === latestDate.getTime();
    });

    return latestObjects;
}
export function getValueNumericForConcept(data: any, conceptName: any, type: any) {
    if (!(data && Array.isArray(data))) return null;
    const matchingObject = data?.find((obj: any) => obj?.concept_name?.toLowerCase() === conceptName?.toLowerCase());
    return matchingObject ? matchingObject[type] : null;
}
export async function saveOfflinePatientData(patientData: any, trigerSyncData = true) {
    const plainPatientData = JSON.parse(JSON.stringify(patientData));
    plainPatientData.program_id = Service.getProgramID() || null;
    plainPatientData.location_id = localStorage.getItem("locationID");
    plainPatientData.provider_id = localStorage.getItem("userID");
    plainPatientData.sync_status = "unsynced";
    plainPatientData.encounter_datetime = Service.getSessionDate();
    const workerStore = useWorkerStore();
    await workerStore.postData("DELETE_RECORD", { storeName: "patientRecords", whereClause: { patientID: plainPatientData.patientID } });
    await workerStore.postData("ADD_OBJECT_STORE", { storeName: "patientRecords", data: plainPatientData });
    const demographicsStore = useDemographicsStore();
    demographicsStore.setRecord(plainPatientData);

    if (useStatusStore().apiStatus && useStatusStore().isSyncingDone && trigerSyncData) {
        await workerStore.postData("SYNC_ALL_DATA", { data: plainPatientData });
    } else if (useStatusStore().apiStatus && trigerSyncData) {
        await workerStore.postData("SAVE_PATIENT_RECORD");
    }
}

/**
 * Insert or override a record in the activeProgramInContext table
 * @param data - The data to be inserted or overridden
 * @returns Promise resolving to void
 */
export async function updateActiveProgramInContext(data: any): Promise<void> {
    // Convert proxy objects to plain objects
    const plainData = JSON.parse(JSON.stringify(data));

    const storeName = "activeProgramInContext";
    const db = await openDatabase(storeName, "program_id");

    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], "readwrite");
        const objectStore = transaction.objectStore(storeName);

        // First, clear all existing records
        const clearRequest = objectStore.clear();

        clearRequest.onsuccess = () => {
            // Then add the new record
            const addRequest = objectStore.add(plainData);

            addRequest.onsuccess = () => resolve();
            addRequest.onerror = () => reject(`Error inserting record: ${addRequest.error}`);
        };

        clearRequest.onerror = () => reject(`Error clearing store: ${clearRequest.error}`);
    });
}

export async function updateConnectionString(data: any): Promise<void> {
    console.log("Updating connection string with data:");
    const plainData = JSON.parse(JSON.stringify(data));

    const storeName = "offlineConnectionString";
    const db = await openDatabase(storeName, "connectin_string_id");

    return new Promise((resolve, reject) => {
        const transaction = db.transaction([storeName], "readwrite");
        const objectStore = transaction.objectStore(storeName);

        // First, clear all existing records
        const clearRequest = objectStore.clear();

        clearRequest.onsuccess = () => {
            // Then add the new record
            const addRequest = objectStore.add(plainData);

            addRequest.onsuccess = () => resolve();
            addRequest.onerror = () => reject(`Error inserting record: ${addRequest.error}`);
        };

        clearRequest.onerror = () => reject(`Error clearing store: ${clearRequest.error}`);
    });


}

export const getConnectonString = async () => {
    const connection_strings = await getOfflineRecords("offlineConnectionString") as any;
    return connection_strings[0].connection_string
}

export const searchPatientFromMODS = async (
  searchCriteria: { 
    given_name?: string; 
    family_name?: string; 
    gender?: string 
  }
) => {
    const BASE_URL = await getConnectonString();
    const params = Object.entries(searchCriteria)
        .filter(([_, value]) => value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&');
    
    const url = `${BASE_URL}/search${params ? `?${params}` : ''}`;
    
    try {
        const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        });
        
        if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const jsonData = await response.json();
        return jsonData.data;
    } catch (error) {
        console.error('Search request failed:', error);
        // throw error;
    }
};

export function getUseMODS(): boolean {
    // Get the value from localStorage
    let useMODS = localStorage.getItem('useMODS');

    // Check if it's empty or null
    if (useMODS === null || useMODS === undefined || useMODS === '') {
        // Set default value (false = Live Server mode)
        useMODS = 'false';
        localStorage.setItem('useMODS', useMODS);
        return false
    }

    // Convert string to boolean and return
    return useMODS === 'true';
}

export async function getOfflinePatientData(patientID: any) {
    if (patientID) {
        const patientRecord = await getOfflineRecords("patientRecords", {
            whereClause: { patientID: patientID },
            currentPage: 1,
            itemsPerPage: 1,
        }).then((data: any) => data.records[0]);

        return patientRecord;
    } else {
        return null;
    }
}

export const updateDemopgraphicStoreONPatientRecordChange = async () => {
    if (getUseMODS() == true) {
        const demographicsStore = useDemographicsStore();
        if (demographicsStore?.patient?.ID) {
            const patientRecord = await getOfflinePatientData(demographicsStore?.patient?.patientID);
            
            if (patientRecord) {
                // Convert both objects to strings
                const patientRecordString = JSON.stringify(patientRecord);
                const demographicsPatientString = JSON.stringify(demographicsStore?.patient);
                // Compare string sizes
                // console.log("Patient ID: ", demographicsStore?.patient?.patientID);
                const patientRecordSize = patientRecordString.length;
                const demographicsPatientSize = demographicsPatientString.length;
                // console.log(`Patient Record String Size: ${patientRecordSize}`);
                // console.log(`Demographics Patient String Size: ${demographicsPatientSize}`);
                if (patientRecordSize === demographicsPatientSize) {
                    console.log("String sizes match!");
                } else {
                    // console.log("String sizes do not match");
                    
                    if (patientRecordSize > demographicsPatientSize) {
                        console.log(`Patient Record is larger by ${patientRecordSize - demographicsPatientSize} characters`);
                        demographicsStore.setPatientRecord(patientRecord);
                    } else {
                        // console.log(`Demographics Patient is larger by ${demographicsPatientSize - patientRecordSize} characters`);
                    }
                }
                // console.log("Patient Record String:", patientRecordString);
                // console.log("Demographics Patient String:", demographicsPatientString);
            }
        }
    }
}

export const getddeNPIDObectViaMODS = async () => {
    const BASE_URL = await getConnectonString();
    const url = `${BASE_URL}/unassigned-npid`;

    try {
        const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        },
        });
        
        if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const jsonData = await response.json();
        return jsonData.data;
    } catch (error) {
        console.error('Search request failed:', error);
        // throw error;
    }
}