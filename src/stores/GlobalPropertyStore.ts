import { defineStore } from "pinia";
import { Service } from "@/services/service";
import { useUserStore } from "@/stores/userStore";
import { toastWarningWithButton } from "@/utils/Alerts";
import { getOfflineRecords } from "@/services/offline_service";
import router from "@/router";
import { useStatusStore } from "@/stores/StatusStore";

const redirectToSetClinicDays = async () => {
    await router.push('clinicaldays')
}

async function getPrograms() {
    const data = await getOfflineRecords("activeProgramInContext");
    return data;
}

export const checkProgramMatch = async (offineProgramIds = [32]) => {
    try {
        const currentPrograms = (await getPrograms()) as any;
        return currentPrograms.some((program: any) => offineProgramIds.includes(program.program_id));
    } catch (error) {
        // console.error("Error checking program match:", error);
        return false;
    }
};

const displayWarningForEnablingNextAppointmentDates = async (facility: any, message: string) => {
    const randomOffset = Math.floor(Math.random() * 7000);
    if ((await checkProgramMatch()) == true) {
        toastWarningWithButton(`${facility.name.toUpperCase()}, ${message}`, 'Click here to configure', redirectToSetClinicDays, 6000 + randomOffset);
    }
};

export const useGlobalPropertyStore = defineStore("globalPropertyStore", {
    state: () => ({
        globalPropertyStore: {
            sitePrefix: false as any,
            dde_enabled: false as any,
            weekDaysPropertiesObj: {} as any,
            maximumNumberOfCForEachDayObj: {} as any,
            holidayDatesObj: {} as any,
        },
    }),
    actions: {
        async loadSitePrefix() {
            try {
                const location_id = localStorage.getItem("locationID");
                const req = await Service.getJson("global_properties", { property: `site_prefix_${location_id}` });
                
                // Check if response is empty (204 No Content) or if the site prefix is not set
                if (!req || !req[`site_prefix_${location_id}`]) {
                    this.globalPropertyStore.sitePrefix = "set_site_prefix";
                } else {
                    this.globalPropertyStore.sitePrefix = req[`site_prefix_${location_id}`];
                }
            } catch (error) {
                const location_id = localStorage.getItem("locationID");
                // Handle any errors by setting default prefix
                this.globalPropertyStore.sitePrefix = `set_site_prefix_${location_id}`;
            }
        },
        async loadDDEStatus() {
            const req = await Service.getJson("global_properties", { property: "dde_enabled" });
            this.globalPropertyStore.dde_enabled = req["dde_enabled"];
        },

        async loadHolidayDateProperty() {
            this.globalPropertyStore.holidayDatesObj = {};
            const user_store = useUserStore();
            const facility = user_store.getfacilityLocation();
            const message = "This facility has not HOLIDAY DAYS configured!";
            try {
                const facility_id = facility.code;
                const req = await Service.getJson("global_properties", { property: "holiday_date_" + facility_id });
                this.globalPropertyStore.holidayDatesObj = req["holiday_date_" + facility_id];

                const obj = JSON.parse(this.globalPropertyStore.holidayDatesObj);
                if (obj.holidayDates.length == 0) {
                    displayWarningForEnablingNextAppointmentDates(facility, message);
                }
            } catch (error) {
                const statusStore = useStatusStore();
                if (statusStore.apiStatus) {
                    displayWarningForEnablingNextAppointmentDates(facility, message);
                }
            }
        },
        async loadWeekDaysProperty() {
            this.globalPropertyStore.weekDaysPropertiesObj = {};
            const user_store = useUserStore();
            const facility = user_store.getfacilityLocation();
            const message = "This facility has no CLINIC DAYS configured!";
            function areAllWeekdaysEnabled(weekDays: any) {
                return (
                    !weekDays.areMondaysDisabled &&
                    !weekDays.areTuesdaysDisabled &&
                    !weekDays.areWednesdaysDisabled &&
                    !weekDays.areThursdaysDisabled &&
                    !weekDays.areFridaysDisabled &&
                    !weekDays.areSaturdaysDisabled &&
                    !weekDays.areSundaysDisabled
                );
            }
            try {
                const facility_id = facility.code;
                const req = await Service.getJson("global_properties", { property: "week_days_properties_" + facility_id });
                this.globalPropertyStore.weekDaysPropertiesObj = req["week_days_properties_" + facility_id];
                const obj = JSON.parse(this.globalPropertyStore.weekDaysPropertiesObj);
                // Add safety check to prevent TypeError
                if (obj && obj.weekDays && areAllWeekdaysEnabled(obj.weekDays)) {
                    displayWarningForEnablingNextAppointmentDates(facility, message);
                }
            } catch (error) {
                // console.log(error);
                displayWarningForEnablingNextAppointmentDates(facility, message);
            }
        },
        async loadMaximumNumberOfCForEachDayProperty() {
            const user_store = useUserStore();
            const facility = user_store.getfacilityLocation() as any;
            this.globalPropertyStore.maximumNumberOfCForEachDayObj = {};
            const message = "This facility has no MAXIMUM NUMBER OF CLIENTS PER DAY configured!";
            try {
                const facility_id = facility.code;
                const req = await Service.getJson("global_properties", { property: "maximum_number_Of_c_for_each_day_" + facility_id });
                this.globalPropertyStore.maximumNumberOfCForEachDayObj = req["maximum_number_Of_c_for_each_day_" + facility_id];
                const obj = JSON.parse(this.globalPropertyStore.maximumNumberOfCForEachDayObj);
                const maxClients = parseInt(obj.maximumNumberOfDaysForEachDay);
                if (maxClients == 0) {
                    displayWarningForEnablingNextAppointmentDates(facility, message);
                }
            } catch (error) {
                displayWarningForEnablingNextAppointmentDates(facility, message);
            }
        },
        async loadGlobalProperty() {
            try {
                await Promise.all([
                    this.loadDDEStatus(),
                    this.loadSitePrefix(),
                    this.loadHolidayDateProperty(),
                    this.loadWeekDaysProperty(),
                    this.loadMaximumNumberOfCForEachDayProperty(),
                ]);
            } catch (error) {
                // Handle any errors that occur during the parallel execution
                // console.error("Error loading global properties:", error);
            }
        },
        async setGlobalProperty(prop: any, val: any) {
            await Service.postJson("global_properties", {
                property: prop,
                property_value: val,
            });
            await this.loadGlobalProperty();
        },
    },
    persist: true,
});
