import { defineStore } from "pinia";
import { getOfflineRecords } from "@/services/offline_service";
import { offineProgramIds } from "@/utils/GeneralUti";

async function getPrograms() {
    const data = await getOfflineRecords("activeProgramInContext");
    return data;
}

export const useStatusStore = defineStore("statusStore", {
    state: () => {
        return {
            apiStatus: true,
            offlineVillageStatus: {} as any,
            offlineDistrictStatus: {} as any,
            offlineCountriesStatus: {} as any,
            offlineTAsStatus: {} as any,
            offlineRelationshipStatus: {} as any,
            offlinePatientsStatus: {} as any,
            offlineDrugsStatus: {} as any,
            isSyncingDone: false,
            syncingTotal: 0 as any,
            syncingCountPercentage: 0 as any,
            shouldProcessPatients: false,
            currentPrograms: [] as any,
        };
    },
    actions: {
        setApiStatus(data: any) {
            this.apiStatus = data;
        },
        setOfflineDistrictStatus(data: any) {
            this.offlineDistrictStatus = data;
        },
        setOfflineCountriesStatus(data: any) {
            this.offlineCountriesStatus = data;
        },
        setOfflineVillageStatus(data: any) {
            this.offlineVillageStatus = data;
        },
        setOfflineTAsStatus(data: any) {
            this.offlineTAsStatus = data;
        },
        setOfflineRelationshipStatus(data: any) {
            this.offlineRelationshipStatus = data;
        },
        setOfflinePatientsStatus(data: any) {
            this.offlinePatientsStatus = data;
        },
        setOfflineDrugsStatus(data: any) {
            this.offlineDrugsStatus = data;
        },
        async checkProgramMatch() {
            try {
                this.currentPrograms = await getPrograms();
                // Check if any program_id in currentPrograms matches the offline program IDs
                this.shouldProcessPatients = this.currentPrograms.some((program: any) => offineProgramIds.includes(program.program_id));
            } catch (error) {
                console.error("Error checking program match:", error);
                this.shouldProcessPatients = false;
            }
        },
        async checkMetaDataStatus() {
            await this.checkProgramMatch();
            this.getSyncingCountPercentage();
            try {
                // Base checks that always apply
                let syncingComplete =
                    this.offlineVillageStatus.total_village >= this.offlineVillageStatus.total &&
                    this.offlineRelationshipStatus.total_relationships >= this.offlineRelationshipStatus.total &&
                    this.offlineCountriesStatus.total_countries >= this.offlineCountriesStatus.total &&
                    this.offlineDistrictStatus.total_districts >= this.offlineDistrictStatus.total &&
                    this.offlineTAsStatus.total_TAs >= this.offlineTAsStatus.total &&
                    this.offlineDrugsStatus.total_OPD_drugs >= this.offlineDrugsStatus.total;

                // Only check patient data if we should process patients
                if (this.shouldProcessPatients) {
                    syncingComplete =
                        syncingComplete && this.offlinePatientsStatus.offlinePatientsCount >= this.offlinePatientsStatus.serverPatientsCount;
                }

                this.isSyncingDone = syncingComplete;
            } catch (error) {
                console.error("Error checking metadata status:", error);
                this.isSyncingDone = false; // Ensure syncing is marked as not done in case of errors
            }
        },
        getSyncingCountPercentage() {
            // Base totals that are always included
            let denominator =
                (this.offlineVillageStatus.total || 0) +
                (this.offlineRelationshipStatus.total || 0) +
                (this.offlineCountriesStatus.total || 0) +
                (this.offlineDistrictStatus.total || 0) +
                (this.offlineTAsStatus.total || 0) +
                (this.offlineDrugsStatus.total || 0);

            let numerator =
                (this.offlineVillageStatus.total_village || 0) +
                (this.offlineRelationshipStatus.total_relationships || 0) +
                (this.offlineCountriesStatus.total_countries || 0) +
                (this.offlineDistrictStatus.total_districts || 0) +
                (this.offlineTAsStatus.total_TAs || 0) +
                (this.offlineDrugsStatus.total_OPD_drugs || 0);

            // Add patient data only if we should process patients
            if (this.shouldProcessPatients) {
                denominator += this.offlinePatientsStatus.serverPatientsCount || 0;
                numerator += this.offlinePatientsStatus.offlinePatientsCount || 0;
            }

            if (denominator === 0) {
                this.syncingCountPercentage = 0;
            } else {
                let percentage = (numerator / denominator) * 100;
                this.syncingCountPercentage = `${percentage ? percentage.toFixed(0) : 0}`;
            }
        },
        async setSyncingTotal() {
            // Fetch all offline records in parallel for better performance
            if (this.apiStatus) {
                await this.checkProgramMatch();

                // Define which resources to fetch
                const resourcesPromises = [
                    getOfflineRecords("villages"),
                    getOfflineRecords("relationship"),
                    getOfflineRecords("countries"),
                    getOfflineRecords("districts"),
                    getOfflineRecords("TAs"),
                    getOfflineRecords("drugs"),
                ];

                // Add patient records fetch only if needed
                let patientRecordsPromise;
                if (this.shouldProcessPatients) {
                    patientRecordsPromise = getOfflineRecords("patientRecords");
                    resourcesPromises.push(patientRecordsPromise);
                }

                // Fetch all resources in parallel
                const results = (await Promise.all(resourcesPromises)) as any;

                // Update statuses for non-patient resources
                this.offlineVillageStatus.total_village = results[0].length;
                this.offlineRelationshipStatus.total_relationships = results[1].length;
                this.offlineCountriesStatus.total_countries = results[2].length;
                this.offlineDistrictStatus.total_districts = results[3].length;
                this.offlineTAsStatus.total_TAs = results[4].length;
                this.offlineDrugsStatus.total_OPD_drugs = results[5].length;

                // Update patient status only if we're processing patients
                if (this.shouldProcessPatients && patientRecordsPromise) {
                    const patientRecords = this.shouldProcessPatients ? results[6] : [];
                    this.offlinePatientsStatus.offlinePatientsCount = patientRecords.length;
                }
            }
        },
    },
    persist: true,
});
